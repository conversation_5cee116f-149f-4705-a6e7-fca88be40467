import axios from "axios";
import Errors from "../errors";
import { Emitter } from "../events/eventEmmiter";
import { projectTypeManage } from "../../config/projectHandler";
import { getconfig } from "../../config";
import { markCompletedGameStatusIf } from "../../interface/cmgApiIf";
import logger from "../../logger";
import { CONSTANTS, exportObject } from "../../constants";
const { PLATFORM } = getconfig();

async function markCompletedGameStatus(data: markCompletedGameStatusIf, token: string, socketId: string, projectType: string): Promise<any> {

    logger.info("markCompletedGameStatus :: ", { data, token });
    const { EVENTS, MESSAGES, NUMERICAL } = exportObject;
    try {

        const CONFIG = await projectTypeManage(projectType);
        if (!CONFIG) {
            throw new Error("Unable to fetch all API");
        }

        const { MARK_COMPLETED_GAME_STATUS } = CONFIG;

        const url = MARK_COMPLETED_GAME_STATUS;
        // logger.info("markCompletedGameStatus :: url :", url);

        const responce = await axios.post(url, data, { headers: { 'Authorization': `${token}`, 'x-mgpapp-key': PLATFORM.APP_KEY, 'x-mgpapp-data': PLATFORM.APP_DATA } })
        // logger.info("markCompletedGameStatus : responce :: ", responce.data);

        if (!responce || !responce.data.success) {
            throw new Errors.InvalidInput('Unable to fetch markCompletedGameStatus data');
        }
        if (responce.data.message === MESSAGES.ERROR.SERVER_UNDER_THE_MAINTENANCE) {
            logger.info(`Server under the maintenance.`, "");
            throw new Errors.UnknownError('Unable to fetch markCompletedGameStatus data');
        }
        return responce;

    } catch (error: any) {
        logger.info('CATCH_ERROR : markCompletedGameStatus :>> ', { error, data, token });
        logger.info("error.response.data ", error.response.data);

        if (error instanceof Errors.UnknownError) {
            let nonProdMsg = "Server under the maintenance!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `markCompletedGameStatus Function Error!`,
            });
        }
        else if (error.response && error.response.data && !error.response.data.success) {
            let nonProdMsg = "Fetch data failed!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `markCompletedGameStatus Function Error!`,
            });
        }
        else {
            let nonProdMsg = "Fetch data failed!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `markCompletedGameStatus Function Error!`,
            });
        }
        throw new Error('Unable to mark Completed Game Status data');
    }
}

const exportedObj = {
    markCompletedGameStatus,
};

export = exportedObj;