import axios from "axios";
import Errors from "../errors";
import { Emitter } from "../events/eventEmmiter";
import { getconfig } from "../../config";
import logger from "../../logger";
import { CONSTANTS, exportObject } from "../../constants";
import { projectTypeManage } from "../../config/projectHandler";
const { PLATFORM } = getconfig();

async function checkMaintanence(token: string, socketId: string, userId: string, projectType: string) {
    logger.info("checkMaintanence ::=>> ", { userId, token });
    const { EVENTS, MESSAGES, NUMERICAL } = exportObject;
    try {

        const CONFIG = await projectTypeManage(projectType);
        if (!CONFIG) {
            throw new Error("Unable to fetch all API");
        }

        const { CHECK_MAINTANENCE } = CONFIG;

        const url = CHECK_MAINTANENCE;
        logger.info("checkMaintanence url :: ", { userId, url });
        // logger.info(userId, "APP_KEY : ", APP_KEY + "APP_DATA : " + APP_DATA);
        let responce = await axios.post(url, {}, { headers: { 'Authorization': `${token}`, 'x-mgpapp-key': PLATFORM.APP_KEY, 'x-mgpapp-data': PLATFORM.APP_DATA } })
        // logger.info(userId, "resData : checkMaintanence responce :: ", JSON.stringify(responce.data));

        let checkMaintanenceDetail = responce.data.data;
        // logger.info(userId, "resData : checkMaintanenceDetail :: ", checkMaintanenceDetail);

        if (!responce || !responce.data.success || !checkMaintanenceDetail) {
            throw new Errors.InvalidInput('Unable to fetch checkBalance data');
        }
        if (checkMaintanenceDetail && checkMaintanenceDetail.isMaintenance) {
            let nonProdMsg = "Server under the maintenance!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `checkMaintanence Function Error !`,
            });
           
        }
        return checkMaintanenceDetail;

    } catch (error: any) {
        logger.info('CATCH_ERROR :  checkMaintanence :>> ', { userId, token, error });
        logger.info("error.response.data ", error.response.data);

        if (error.response && error.response.data && !error.response.data.success) {
            let nonProdMsg = "Fetch data failed!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `checkMaintanence Function Error !`,
            });
           
        }
        else {
            let nonProdMsg = "Fetch data failed!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `checkMaintanence : Fetch data failed!`,
            });
        }
        throw new Error('Server under the maintenance!');
    }
}

const exportedObj = {
    checkMaintanence,
};

export = exportedObj;