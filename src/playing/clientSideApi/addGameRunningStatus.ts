import axios from "axios";
import Errors from "../errors";
import { Emitter } from "../events/eventEmmiter";
import { CONSTANTS, exportObject } from "../../constants";
import { addGameRunningStatusIf } from "../../interface/cmgApiIf";
import logger from "../../logger";
import { projectTypeManage } from "../../config/projectHandler";
import { getconfig } from "../../config";
const { MESSAGES, NUMERICAL } = exportObject;

async function addGameRunningStatus(data: addGameRunningStatusIf, token: string, socketId: string, userId: string, projectType: string) {
    logger.info("addGameRunningStatusDetail [userId, data, token] : ", { userId, data, token });
    try {

        const CONFIG = await projectTypeManage(projectType);
        if (!CONFIG) { throw new Error("Unable to fetch all API"); }

        const { PLATFORM } = getconfig();
        const { ADD_GAME_RUNNING_STATUS } = CONFIG;

        const url = ADD_GAME_RUNNING_STATUS;
        logger.info("addGameRunningStatusDetail url :: ", url);
        logger.info("APP_KEY , APP_DATA :: ", { APP_KEY: PLATFORM.APP_KEY, APP_DATA: PLATFORM.APP_DATA });
        let responce = await axios.post(url, data, { headers: { 'Authorization': `${token}`, 'x-mgpapp-key': PLATFORM.APP_KEY, 'x-mgpapp-data': PLATFORM.APP_DATA } })

        let addGameRunningStatusDetail = responce.data.data;
        logger.info("resData : addGameRunningStatusDetail :: ", addGameRunningStatusDetail);

        if (!responce || !responce.data.success || !addGameRunningStatusDetail) {
            throw new Errors.InvalidInput('Unable to fetch add Game Running Status data');
        }
        if (responce.data.message === MESSAGES.ERROR.SERVER_UNDER_THE_MAINTENANCE) {
            logger.info(userId, `Server under the maintenance.`)
            throw new Errors.UnknownError('Unable to fetch add Game Running Status data');
        }
        return addGameRunningStatusDetail;


    } catch (error: any) {
        logger.info('CATCH_ERROR :  addGameRunningStatusDetail [userId, data, token, error] :>> ', { userId, data, token, error });


        if (error instanceof Errors.UnknownError) {
            let nonProdMsg = "Server under the maintenance!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `addGameRunningStatus Function Error !`,
            });
        }
        else if (error?.response && error?.response?.data && !error?.response?.data?.success) {
            let nonProdMsg = "Fetch data failed!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `addGameRunningStatus Function Error !`,
            });
        }
        else {
            let nonProdMsg = "Fetch data failed!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `addGameRunningStatus Function Error !`,
            });
        }
        throw error;
    }
}


const exportedObj = {
    addGameRunningStatus,
};


export = exportedObj;