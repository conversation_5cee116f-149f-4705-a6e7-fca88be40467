import axios from 'axios';
import Errors from "../errors";
import { Emitter } from '../events/eventEmmiter';
import { getconfig } from '../../config';
import { CONSTANTS, exportObject } from '../../constants';
import { projectTypeManage } from '../../config/projectHandler';
import logger from '../../logger';
const { PLATFORM } = getconfig();



async function rediusCheck(gameId: string, token: string, socketId: string, projectType: string): Promise<any> {
  logger.info("rediusCheck :: ", { gameId, token });
  const { EVENTS, MESSAGES, NUMERICAL } = exportObject;
  try {

    const CONFIG = await projectTypeManage(projectType);
    if (!CONFIG) {
      throw new Error("Unable to fetch all API");
    }

    const { REDIUS_CHECK } = CONFIG;

    const url = REDIUS_CHECK;

    let responce = await axios.post(url, { gameId }, { headers: { 'Authorization': `${token}`, 'x-mgpapp-key': PLATFORM.APP_KEY, 'x-mgpapp-data': PLATFORM.APP_DATA } });
    // logger.info("rediusCheck : responce :: ", responce.data);

    const result = responce.data.data;
    // logger.info("resData : rediusCheck result ::", result)

    if (!responce || !responce.data || !responce.data.success) {
      throw new Errors.InvalidInput('Unable to fetch redius Check data');
    }
    if (responce.data.message === MESSAGES.ERROR.SERVER_UNDER_THE_MAINTENANCE) {
      logger.info(`Server under the maintenance.`, "");
      throw new Errors.UnknownError('Unable to fetch redius Check data');
    }
    return result;
  } catch (error: any) {
    logger.info("CATCH_ERROR : rediusCheck :: ", { token, error });
    logger.info("error.response.data ", error.response.data);

    if (error instanceof Errors.UnknownError) {
      let nonProdMsg = "Server under the maintenance!";
      Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
        sentId: socketId,
        data: {
          error: true,
          message: `${nonProdMsg}`
        },
        message: `rediusCheck Function Error!`,
      });
    }
    else if (error.response && error.response.data && !error.response.data.success) {
      let nonProdMsg = "Fetch data failed!";
      Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
        sentId: socketId,
        data: {
          error: true,
          message: `${nonProdMsg}`
        },
        message: `rediusCheck Function Error!`,
      });
    }
    else {
      let nonProdMsg = "Fetch data failed!";
      Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
        sentId: socketId,
        data: {
          error: true,
          message: `${nonProdMsg}`
        },
        message: `rediusCheck Function Error!`,
      });
    }
    throw new Error('Unable to redius Check data');

  }
}

const exportedObj = {
  rediusCheck,
};
export = exportedObj;