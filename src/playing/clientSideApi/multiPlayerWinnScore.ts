import axios from "axios";
import Errors from "../errors";
import { Emitter } from "../events/eventEmmiter";
import { projectTypeManage } from "../../config/projectHandler";
import { getconfig } from "../../config";
import { CONSTANTS, exportObject } from "../../constants";
import logger from "../../logger";
const { PLATFORM } = getconfig();

async function multiPlayerWinnScore(data: any, token: string, socketId: string, projectType: string) {
    logger.info("multiPlayerWinnScore :: ", { data, token });
    const { EVENTS, MESSAGES, NUMERICAL } = exportObject;
    try {

        const CONFIG = await projectTypeManage(projectType);
        if (!CONFIG) {
            throw new Error("Unable to fetch all API");
        }

        const { MULTI_PLAYER_SUBMIT_SCORE } = CONFIG;

        const url = MULTI_PLAYER_SUBMIT_SCORE;

        const responce = await axios.post(url, data, { headers: { 'Authorization': `${token}`, 'x-mgpapp-key': PLATFORM.APP_KEY, 'x-mgpapp-data': PLATFORM.APP_DATA } })
        logger.info("multiPlayerWinnScore : responce :: ", responce.data);

        const multiPlayerSubmitScoreData = responce.data.data
        logger.info("resData : multiPlayerSubmitScore :: ", multiPlayerSubmitScoreData);

        if (!responce || !responce.data.success || !multiPlayerSubmitScoreData) {
            throw new Errors.InvalidInput('Unable to fetch multiPlayerSubmitScoreData data');
        }
        if (responce.data.message === MESSAGES.ERROR.SERVER_UNDER_THE_MAINTENANCE) {
            logger.info(`Server under the maintenance.`, "");
            throw new Errors.UnknownError('Unable to fetch multiPlayerSubmitScoreData data');
        }

        return multiPlayerSubmitScoreData;

    } catch (error: any) {
        logger.info('CATCH_ERROR : multi Player Winn Score:  :>> ', { error, data, token });
        logger.info("error.response.data ", error.response.data);

        if (error instanceof Errors.UnknownError) {
            let nonProdMsg = "Server under the maintenance!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `multiPlayerWinnScore Function Error!`,
            });
        }
        else if (error.response && error.response.data && !error.response.data.success) {
            let nonProdMsg = "Fetch data failed!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `multiPlayerWinnScore Function Error!`,
            });
        }
        else {
            let nonProdMsg = "Fetch data failed!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `multiPlayerWinnScore Function Error!`,
            });
        }
        throw new Error('Unable to multi Player Winn Score data');

    }


}

const exportedObj = {
    multiPlayerWinnScore,
};

export = exportedObj;