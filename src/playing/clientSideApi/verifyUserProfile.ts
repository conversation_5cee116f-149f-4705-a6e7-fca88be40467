import axios from 'axios';
import Errors from "../errors";
import { Emitter } from '../events/eventEmmiter';
import { getconfig } from '../../config';
import logger from '../../logger';
import { CONSTANTS, exportObject } from '../../constants';
import { projectTypeManage } from '../../config/projectHandler';
const { PLATFORM } = getconfig();

async function verifyUserProfile(token: string, socketId: string, projectType: string): Promise<any> {
  logger.info("verifyUserProfile :: ", token);
  const { EVENTS, MESSAGES, NUMERICAL } = exportObject;
  try {

    const CONFIG = await projectTypeManage(projectType);
    if (!CONFIG) {
      throw new Error("Unable to fetch all API");
    }

    const { VERIFY_USER_PROFILE } = CONFIG;

    const url = VERIFY_USER_PROFILE;
    // logger.info("APP_KEY :: ", APP_KEY, "APP_DATA :: ", APP_DATA, "GAME_ID :: >>", GAME_ID)
    let responce = await axios.post(url, { gameId: PLATFORM.GAME_ID }, { headers: { 'Authorization': `${token}`, 'x-mgpapp-key': PLATFORM.APP_KEY, 'x-mgpapp-data': PLATFORM.APP_DATA } });
    // logger.info("verifyUserProfile : responce :: ", responce.data);

    const result = responce.data.data;
    // logger.info("resData : result :: ", result);

    if (!responce || !result) {
      throw new Error('Unable to fetch verify User Profile data');
    }
    else if (result.isValidUser === false) {
      logger.info("isValidUser  ==>> ", result.isValidUser)
      //await decrCounter(REDIS.ONLINEPLAYER);
      throw new Errors.InvalidInput('Unable to fetch verify User Profile data');
    }
    if (responce.data.message === MESSAGES.ERROR.SERVER_UNDER_THE_MAINTENANCE) {
      logger.info(`Server under the maintenance.`, "");
      throw new Errors.UnknownError('Unable to fetch verify User Profile data');
    }
    return result;
  } catch (error: any) {
    logger.info("CATCH_ERROR : getUserProfile :: ", { token, error });

    if (error instanceof Errors.UnknownError) {
      let nonProdMsg = "Server under the maintenance!";
      Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
        sentId: socketId,
        data: {
          error: true,
          message: `${nonProdMsg}`
        },
        message: `verifyUserProfile Function Error!`,
      });
    }
    else if (error.response && error.response.data && !error.response.data.success) {
      let nonProdMsg = "Fetch data failed!";
      Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
        sentId: socketId,
        data: {
          error: true,
          message: `${nonProdMsg}`
        },
        message: `verifyUserProfile Function Error!`,
      });
    }
    else {
      let nonProdMsg = "Fetch data failed!";
      Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
        sentId: socketId,
        data: {
          error: true,
          message: `${nonProdMsg}`
        },
        message: `verifyUserProfile Function Error!`,
      });
    }
    throw new Error('Unable to verify User Profile data');
  }
}

const exportedObj = {
  verifyUserProfile,
};
export = exportedObj;
