import axios from 'axios';
import Errors from "../errors";
import { Emitter } from '../events/eventEmmiter';
import { getconfig } from '../../config';
import logger from '../../logger';
import { CONSTANTS, exportObject } from '../../constants';
import { projectTypeManage } from '../../config/projectHandler';
const { PLATFORM } = getconfig();


async function getUserOwnProfile(token: string, socketId: string, projectType: string): Promise<any> {
  logger.info("getUserOwnProfile :: ", token);
  const { EVENTS, MESSAGES, NUMERICAL } = exportObject;
  try {

    const CONFIG = await projectTypeManage(projectType);
    if (!CONFIG) {
      throw new Error("Unable to fetch all API");
    }

    const { GET_USER_OWN_PROFILE } = CONFIG;

    const url = GET_USER_OWN_PROFILE
    // logger.info("getUserOwnProfile url :: ", url)

    const responce = await axios.post(url, {}, { headers: { 'Authorization': `${token}`, 'x-mgpapp-key': PLATFORM.APP_KEY, 'x-mgpapp-data': PLATFORM.APP_DATA } });
    // logger.info("getUserOwnProfile : responce :: ", responce.data);

    const userProfileDetail = responce.data.data
    // logger.info("resData : userProfileDetail :: ", userProfileDetail);

    if (!responce || !responce.data.success || !userProfileDetail) {
      throw new Errors.InvalidInput('Unable find user profile failed!');
    }
    if (responce.data.message === MESSAGES.ERROR.SERVER_UNDER_THE_MAINTENANCE) {
      logger.info(`Server under the maintenance.`, "");
      throw new Errors.UnknownError('Unable find user profile failed!');
    }
    return userProfileDetail;

  } catch (error: any) {
    logger.info("CARCH_ERROR: getUserOwnProfile ::", { token, error });
    logger.info("error.response.data ", error.response.data);

    if (error instanceof Errors.UnknownError) {
      let nonProdMsg = "Server under the maintenance!";
      Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
        sentId: socketId,
        data: {
          error: true,
          message: `${nonProdMsg}`
        },
        message: `getUserOwnProfile Function Error!`,
      });
    }
    else if (error.response && error.response.data && !error.response.data.success) {
      console.log("CARCH_ERROR: ERR>:", error.response.data);
      let nonProdMsg = "Fetch data failed!";
      Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
        sentId: socketId,
        data: {
          error: true,
          message: `${nonProdMsg}`
        },
        message: `getUserOwnProfile Function Error!`,
      });
    }
    else {
      let nonProdMsg = "Fetch data failed!";
      Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
        sentId: socketId,
        data: {
          error: true,
          message: `${nonProdMsg}`
        },
        message: `getUserOwnProfile Function Error!`,
      });
    }
    throw new Error('Unable to get User Own Profile data');

  }
}

const exportedObj = {
  getUserOwnProfile,
};

export = exportedObj;
