import axios from "axios";
import Errors from "../errors";
import { Emitter } from "../events/eventEmmiter";
import { projectTypeManage } from "../../config/projectHandler";
import { getconfig } from "../../config";
import logger from "../../logger";
import { CONSTANTS, exportObject } from "../../constants";

const { PLATFORM } = getconfig();

async function checkUserBlockStatus(tablePlayerIds: string[], token: string, socketId: string, projectType: string): Promise<any> {

    logger.info("checkUserBlockStatus :: ", { tablePlayerIds, token });
    const { EVENTS, MESSAGES, NUMERICAL } = exportObject;
    try {

        const CONFIG = await projectTypeManage(projectType);
        if (!CONFIG) {
            throw new Error("Unable to fetch all API");
        }

        const { CHECK_USER_BLOCK_STATUS } = CONFIG;

        const url = CHECK_USER_BLOCK_STATUS;
        // logger.info("checkUserBlockStatus :: url :", url);

        const responce = await axios.post(url, { tablePlayerIds }, { headers: { 'Authorization': `${token}`, 'x-mgpapp-key': PLATFORM.APP_KEY, 'x-mgpapp-data': PLATFORM.APP_DATA } })
        // logger.info("checkUserBlockStatus : responce :: ", responce.data);

        const checkUserBlockStatusData = responce.data.data
        // logger.info("resData : checkUserBlockStatus :: ", checkUserBlockStatusData);

        if (!responce || !responce.data.success || !checkUserBlockStatusData) {
            throw new Errors.InvalidInput('Unable to fetch checkUserBlockStatus data');
        }
        if (responce.data.message === MESSAGES.ERROR.SERVER_UNDER_THE_MAINTENANCE) {
            logger.info(`Server under the maintenance.`, "");
            throw new Errors.UnknownError('Unable to fetch checkUserBlockStatus data');
        }
        return checkUserBlockStatusData.isUserBlock;

    } catch (error: any) {
        logger.info('CATCH_ERROR : checkUserBlockStatus :>> ', { error, tablePlayerIds, token });
        logger.info("error.response.data ", error.response.data);

        if (error instanceof Errors.UnknownError) {
            let nonProdMsg = "Server under the maintenance!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `checkUserBlockStatus Function Error!`,
            });
        }
        else if (error.response && error.response.data && !error.response.data.success) {
            let nonProdMsg = "Fetch data failed!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `checkUserBlockStatus Function Error!`,
            });
        }
        else {
            let nonProdMsg = "Fetch data failed!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `checkUserBlockStatus Function Error!`,
            });
        }
        throw new Error('Unable to check User Block Status data');

    }


}

const exportedObj = {
    checkUserBlockStatus,
};

export = exportedObj;