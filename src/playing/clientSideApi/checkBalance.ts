import axios from "axios";
import { Emitter } from "../events/eventEmmiter";
import { getconfig } from "../../config";
import logger from "../../logger";
import Errors from "../errors";
import { checkBalanceIf } from "../../interface/cmgApiIf";
import { projectTypeManage } from "../../config/projectHandler";
import { CONSTANTS, exportObject } from "../../constants";
const { PLATFORM } = getconfig();

async function checkBalance(data: checkBalanceIf, token: string, socketId: string, projectType: string) {
    logger.info("checkBalance ", { data, token });
    const { EVENTS, MESSAGES, NUMERICAL } = exportObject;
    try {

        const CONFIG = await projectTypeManage(projectType);
        if (!CONFIG) {
            throw new Error("Unable to fetch all API");
        }

        const { CHECK_BALANCE } = CONFIG;

        const url = CHECK_BALANCE as string;
        // logger.info("checkBalance url :: ", url);
        // logger.info("APP_KEY : ", APP_KEY, "APP_DATA : ", APP_DATA);
        let responce = await axios.post(url, data, { headers: { 'Authorization': `${token}`, 'x-mgpapp-key': PLATFORM.APP_KEY, 'x-mgpapp-data': PLATFORM.APP_DATA } })

        // logger.info("resData : checkBalance responce :: ", responce.data);

        let checkBalanceDetail = responce.data.data;
        // logger.info("resData : checkBalanceDetail :: ", checkBalanceDetail);

        if (!responce || !responce.data.success || !checkBalanceDetail) {
            throw new Errors.InvalidInput('Unable to fetch checkBalance data');
        }
        if (responce.data.message === MESSAGES.ERROR.SERVER_UNDER_THE_MAINTENANCE) {
            logger.info(`Server under the maintenance.`, "")
            throw new Errors.UnknownError('Unable to fetch checkBalance data');
        }
        return checkBalanceDetail;

    } catch (error: any) {
        logger.info('CATCH_ERROR :  checkBalance :>> ', { data, token, error });
        logger.info("error.response.data ", error.response.data);

        if (error instanceof Errors.UnknownError) {
            let nonProdMsg = "Server under the maintenance!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `checkBalance Function Error !`,
            });
           
        }
        else if (error.response && error.response.data && !error.response.data.success) {
            let nonProdMsg = "Fetch data failed!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `checkBalance Function Error !`,
            });
        }
        else {
            let nonProdMsg = "Fetch data failed!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `checkBalance Function Error !`,
            });
        }
        throw new Error('Unable to check Balance data');
    }
}

const exportedObj = {
    checkBalance,
};

export = exportedObj;