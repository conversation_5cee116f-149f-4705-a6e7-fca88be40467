import axios from "axios";
import Errors from "../errors";
import { Emitter } from "../events/eventEmmiter";
import { projectTypeManage } from "../../config/projectHandler";
import { getconfig } from "../../config";
import { walletDebitIf } from "../../interface/cmgApiIf";
import logger from "../../logger";
import { CONSTANTS, exportObject } from "../../constants";
const { PLATFORM } = getconfig();

async function wallateDebit(data: walletDebitIf, token: string, socketId: string, projectType: string) {
  logger.info("wallateDebit :: ", { data, token });
  const { EVENTS, MESSAGES, NUMERICAL } = exportObject;
  try {

    const CONFIG = await projectTypeManage(projectType);
    if (!CONFIG) {
      throw new Error("Unable to fetch all API");
    }

    const { DEDUCT_USER_ENTRY_FEE } = CONFIG;

    const url = DEDUCT_USER_ENTRY_FEE;
    let responce = await axios.post(url, data, { headers: { 'Authorization': `${token}`, 'x-mgpapp-key': PLATFORM.APP_KEY, 'x-mgpapp-data': PLATFORM.APP_DATA } })
    // logger.info("wallateDebit : responce :: ", responce.data);

    let debitAmountDetail = responce.data.data;
    // logger.info("resData : debitAmountDetail :: ", debitAmountDetail);

    if (!responce || !responce.data.success || !debitAmountDetail) {
      throw new Errors.InvalidInput('Unable to fetch collect amount data');
    }
    if (responce.data.message === MESSAGES.ERROR.SERVER_UNDER_THE_MAINTENANCE) {
      throw new Errors.UnknownError('Unable to fetch collect amount data');
    }
    return debitAmountDetail;
  } catch (error: any) {
    logger.info('CATCH_ERROR :  wallateDebit :>> ', { data, token, error });
    logger.info("error.response.data ", error.response.data);

    if (error instanceof Errors.UnknownError) {
      let nonProdMsg = "Server under the maintenance!";
      Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
        sentId: socketId,
        data: {
          error: true,
          message: `${nonProdMsg}`
        },
        message: `wallateDebit Function Error!`,
      });
    }
    else if (error.response && error.response.data && !error.response.data.success) {
      let nonProdMsg = "Fetch data failed!";
      Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
        sentId: socketId,
        data: {
          error: true,
          message: `${nonProdMsg}`
        },
        message: `wallateDebit Function Error!`,
      });
    }
    else {
      let nonProdMsg = "Fetch data failed!";
      Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
        sentId: socketId,
        data: {
          error: true,
          message: `${nonProdMsg}`
        },
        message: `wallateDebit Function Error!`,
      });
    }
    throw new Error('Unable to wallate Debit data');

  }
}

const exportedObj = {
  wallateDebit,
};

export = exportedObj;