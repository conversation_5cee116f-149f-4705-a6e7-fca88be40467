import axios from "axios";
import Errors from "../errors";
import { projectTypeManage } from "../../config/projectHandler";
import { multiPlayerDeductEntryFeeIf, multiPlayerDeductEntryFeeResponse } from "../../interface/cmgApiIf";
import logger from "../../logger";
import { getconfig } from "../../config";


async function multiPlayerDeductEntryFee(data: multiPlayerDeductEntryFeeIf, token: string, socketId: string, projectType: string): Promise<multiPlayerDeductEntryFeeResponse | undefined> {
    const tableId = data.tableId;
    logger.info("multiPlayerDeductEntryFee :: ", { socketId, tableId, data, token })
    try {

        const CONFIG = await projectTypeManage(projectType);
        if (!CONFIG) {
            throw new Error("Unable to fetch all API");
        }

        const { MULTI_PLAYER_DEDUCT_ENTRY_FEE } = CONFIG;

        const { PLATFORM } = getconfig();
        const url = MULTI_PLAYER_DEDUCT_ENTRY_FEE;
        let responce = await axios.post(url, data, { headers: { 'Authorization': `${token}`, 'x-mgpapp-key': PLATFORM.APP_KEY, 'x-mgpapp-data': PLATFORM.APP_DATA } })
        // logger.info("multiPlayerDeductEntryFee : responce :: ", responce.data);

        let multiPlayerDeductEntryFeeDetails = responce.data.data;
        // logger.info(tableId, "resData : multiPlayerDeductEntryFee :: ", multiPlayerDeductEntryFeeDetails);

        if (!responce || !responce.data || !multiPlayerDeductEntryFeeDetails) {
            throw new Errors.CancelBattle('Unable to fetch collect amount data');
        }
        return multiPlayerDeductEntryFeeDetails;

    } catch (error: any) {
        logger.info("error.response.data ", error);
        logger.info('CATCH_ERROR :  multiPlayerDeductEntryFeeDetails :>> ', { tableId, data, token, error });
        // throw new Errors.CancelBattle("get multi Player Deduct Entry Fee fail");
        return undefined;
    }
}

const exportedObj = {
    multiPlayerDeductEntryFee,
};

export = exportedObj;