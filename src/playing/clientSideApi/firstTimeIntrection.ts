import axios from "axios";
import Errors from "../errors";
import { Emitter } from "../events/eventEmmiter";
import { projectTypeManage } from "../../config/projectHandler";
import logger from "../../logger";
import { CONSTANTS, exportObject } from "../../constants";
import { getconfig } from "../../config";
const { PLATFORM } = getconfig();

async function firstTimeIntrection(gameId: string, gameModeId: string, token: string, socketId: string, projectType: string): Promise<any> {
    logger.info("firstTimeIntrection ::> ", { gameId, token, socketId, gameModeId })
    const { EVENTS, MESSAGES, NUMERICAL } = exportObject;
    try {

        const CONFIG = await projectTypeManage(projectType);
        if (!CONFIG) {
            throw new Error("Unable to fetch all API");
        }

        const { FTUE_UPDATE } = CONFIG;

        const url = FTUE_UPDATE;
        let responce: any;

        if (gameModeId === "") {

            responce = await axios.post(url, { gameId }, { headers: { 'Authorization': `${token}`, 'x-mgpapp-key': PLATFORM.APP_KEY, 'x-mgpapp-data': PLATFORM.APP_DATA } });
        } else {

            responce = await axios.post(url, { gameId, gameModeId }, { headers: { 'Authorization': `${token}`, 'x-mgpapp-key': PLATFORM.APP_KEY, 'x-mgpapp-data': PLATFORM.APP_DATA } });
        }
        // logger.info("resData firstTimeIntrection : : >> ", responce.data);

        let firstTimeIntrectionDetail = responce.data;
        // logger.info("resData : firstTimeIntrectionDetail :: ", firstTimeIntrectionDetail);

        if (!responce || !responce.data.success || !firstTimeIntrectionDetail) {
            throw new Errors.InvalidInput('Unable to fetch firstTimeIntrection data');
        }
        if (responce.data.message === MESSAGES.ERROR.SERVER_UNDER_THE_MAINTENANCE) {
            logger.info(`Server under the maintenance.`, "");
            throw new Errors.UnknownError('Unable to fetch firstTimeIntrection data');
        }
        return true;

    } catch (error: any) {
        logger.info('CATCH_ERROR :  firstTimeIntrection :>> ', { gameId, token, error });
        logger.info("error.response.data ", error.response.data);

        if (error instanceof Errors.UnknownError) {
            let nonProdMsg = "Server under the maintenance!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `firstTimeIntrection Function Error!`,
            });
        }
        else if (error.response && error.response.data && !error.response.data.success) {
            let nonProdMsg = "Fetch data failed!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `firstTimeIntrection Function Error!`,
            });
        }
        else {
            let nonProdMsg = "Fetch data failed!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `firstTimeIntrection Function Error!`,
            });
        }
        throw new Error('Unable to first Time Intrection data');
    }
}

const exportedObj = {
    firstTimeIntrection,
};

export = exportedObj;