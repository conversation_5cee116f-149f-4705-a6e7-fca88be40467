import axios from 'axios';
import Errors from "../errors";
import { Emitter } from '../events/eventEmmiter';
import { getconfig } from '../../config';
import { CONSTANTS, exportObject } from '../../constants';
import logger from '../../logger';
import { projectTypeManage } from '../../config/projectHandler';
const { PLATFORM } = getconfig();

async function gameSettinghelp(gameId: any, token: string, socketId: string, projectType: string) {
    logger.info("gmaneSettinghelp : :  ", { gameId, token });
    const { EVENTS, MESSAGES, NUMERICAL } = exportObject;
    try {

        const CONFIG = await projectTypeManage(projectType);
        if (!CONFIG) {
            throw new Error("Unable to fetch all API");
        }

        const { GAME_SETTING_MENU_HELP } = CONFIG;

        const url = GAME_SETTING_MENU_HELP

        const responce = await axios.post(url, { gameId }, { headers: { 'Authorization': `${token}`, 'x-mgpapp-key': PLATFORM.APP_KEY, 'x-mgpapp-data': PLATFORM.APP_DATA } });
        // logger.info("resData gmaneSettinghelp : : >> ", responce.data);

        const rules = responce.data.data;
        // logger.info("resData : gameSettinghelp rules :: ", rules);

        if (!responce || !responce.data.success || !rules) {
            throw new Errors.InvalidInput('Unable to fetch gmaneSettinghelp data');
        }
        if (responce.data.message === MESSAGES.ERROR.SERVER_UNDER_THE_MAINTENANCE) {
            logger.info(`Server under the maintenance.`, "");
            throw new Errors.UnknownError('Unable to fetch gmaneSettinghelp data');
        }
        return rules;

    } catch (error: any) {
        logger.info('CATCH_ERROR : gmaneSettinghelp :: ', { gameId, token, error });
        logger.info("error.response.data ", error.response.data);

        if (error instanceof Errors.UnknownError) {
            let nonProdMsg = "Server under the maintenance!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `gameSettinghelp Function Error!`,
            });
        }
        else if (error.response && error.response.data && !error.response.data.success) {
            let nonProdMsg = "Fetch data failed!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `gameSettinghelp Function Error!`,
            });
        }
        else {
            let nonProdMsg = "Fetch data failed!";
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socketId,
                data: {
                    error: true,
                    message: `${nonProdMsg}`
                },
                message: `gameSettinghelp Function Error!`,
            });
        }
        throw new Error('Unable to game Setting help data');

    }

}

const exportedObj = {
    gameSettinghelp,
};

export = exportedObj; 