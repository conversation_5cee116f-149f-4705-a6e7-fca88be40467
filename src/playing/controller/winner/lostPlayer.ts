import { Socket } from "socket.io";
import { CONSTANTS } from "../../../constants";
import logger from "../../../logger";
import { getPlayerGamePlay, updatePlayerGamePlay } from "../../database/pgp";
import { Emitter } from "../../events/eventEmmiter";
import { T_TICKET_TYPE } from "../../../interface/common";

export const lostPlayer = async (
    userId: string,
    tableId: string,
    socket: Socket,
    pattern: T_TICKET_TYPE,
    errorMsg: string,
    ticketNumber: number,
): Promise<void> => {

    logger.info("In lostPlayer.","");

    try {

        if (!userId || !pattern || !errorMsg) {
            logger.info("lostPlayer Validation:: ", { userId, pattern, errorMsg });
            throw new Error("lostPlayer Request Data Not Found");
        }

        let getPGP = await getPlayerGamePlay(userId, tableId);
        if (!getPGP) { throw new Error("Get PlayerGamePlay Data Failed!"); }

        getPGP.isLost = true;
        getPGP.lostList.push({ ticketNo: ticketNumber, message: errorMsg });

        getPGP = await updatePlayerGamePlay(getPGP);
        if (!getPGP) { throw new Error("Update PlayerGamePlay Failed !!"); }

        Emitter.emit(CONSTANTS.EVENTS.ERROR_TICKET, {
            sentId: socket.id,
            data: { invalidTicket: getPGP.lostList },
            message: `${pattern} Function Error !`,
        });

    } catch (error) {
        logger.info("[lostPlayer] Error: ", error);
    }

}