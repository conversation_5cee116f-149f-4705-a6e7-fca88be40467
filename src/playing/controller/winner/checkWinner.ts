import { delayWinningQueue } from "../../../bull/queue/delayWinningTimerQueue";
import { CONSTANTS } from "../../../constants";
import { I_WINNING_TIMER } from "../../../interface/common";
import logger from "../../../logger";
import { getPlayerGamePlay } from "../../database/pgp";
import { getPlayer } from "../../database/player";
import { getTableGamePlay, updateTableGamePlay } from "../../database/table";
import { Emitter } from "../../events/eventEmmiter";
import { winOrLostMoney } from "../../helper/scoreFinder";



export const checkWinner = async (
    tableId: string,
    userId: string,
    isDisconectOrLeave: boolean, // if only one player in game and player has been disconnect or leave = true, In Other case False
) => {

    logger.info("In checkWinner.", { tableId, userId });

    try {

        if (!tableId || !userId) {
            logger.info("CheckWinner Validation:: ", { tableId, userId });
            throw new Error("checkWinner Request Data Not Found");
        }

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        getTGP.lock = true;
        getTGP.tableState = CONSTANTS.STATE.WIN;

        getTGP = await updateTableGamePlay(getTGP);
        if (!getTGP) { throw new Error("Update TableGamePlay Data Failed !"); }

        logger.info("playerList : ", getTGP.playerList);

        for (let i = 0; i < getTGP.playerList.length; i++) {
            const userIds = getTGP.playerList[i].userId;
            logger.info("userIds : ", userIds);

            let player = await getPlayer(userIds);
            if (!player) { throw new Error("Get Player Data Failed!"); }

            let getPGP = await getPlayerGamePlay(userIds, tableId);
            if (!getPGP) { throw new Error("Get PlayerGamePlay Data Failed!"); }


            logger.info("Both False : ", { IsDisconnect: getPGP.isDisconnect, isLeave: getPGP.isLeave })

            if (getPGP && !getPGP.isDisconnect && !getPGP.isLeave) {

                const winOrlostData = await winOrLostMoney(userIds, tableId);
                logger.info("winOrlostData : ", winOrlostData);

                Emitter.emit(CONSTANTS.EVENTS.WINORLOSS, {
                    sentId: player.socketId,
                    data: { message: winOrlostData },
                    message: `Success - TableId : ${tableId}`,
                });

            }

        }

        const winningBullData: I_WINNING_TIMER = { userId, tableId, isDisconectOrLeave };

        await delayWinningQueue(winningBullData);

        return;

    } catch (error) {
        logger.info("[checkWinner] Error: ", error);
    }

}