import { getconfig } from "../../../config";
import { io } from "../../../connection/socket";
import { CONSTANTS } from "../../../constants";
import { I_COLLECTENTRYFEE, I_MATCH_MAKING } from "../../../interface/common";
import logger from "../../../logger";
import { markCompletedGameStatus } from "../../clientSideApi";
import { multiPlayerDeductEntryFee } from "../../clientSideApi/multiPlayerDeductEntryFee";
import { getPlayer, updatePlayer } from "../../database/player";
import { getTableGamePlay } from "../../database/table";
import { Emitter } from "../../events/eventEmmiter";
import { matchMaking } from "../matchMaking/matchmaking";
import { removeDataPlayer, removeOnlyTable, tableLeavePlayerListHandler } from "../removeData/removeDbData";


export const multiPlayerEntryFeeDeduction = async (data: I_COLLECTENTRYFEE): Promise<boolean> => {

    try {

        const { GAME_START_TIMER } = getconfig().GAMEPLAY;

        let getTGP = await getTableGamePlay(data.tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        let player = await getPlayer(data.userIds[0]);
        if (!player) { throw new Error("Get Player Data Failed!"); }

        const apiData = {
            tableId: getTGP._id,
            tournamentId: player.lobbyId,
            userIds: data.userIds
        }

        const multiPlayerDeductEntryFeeData = await multiPlayerDeductEntryFee(apiData, player.token, player.socketId, player.projectType);
        logger.info("MultiPlayerDeductEntryFeeData :: ", { tableId: data.tableId, multiPlayerDeductEntryFeeData });

        if (!multiPlayerDeductEntryFeeData || !multiPlayerDeductEntryFeeData.isMinPlayerEntryFeeDeducted) {
            logger.info("Game stopped on the API side due to the entryfee response not Found !!", "");

            if (multiPlayerDeductEntryFeeData) {

                for (let i = 0; i < multiPlayerDeductEntryFeeData.notDeductedUserIds.length; i++) {
                    const userId = multiPlayerDeductEntryFeeData.notDeductedUserIds[i];

                    let player = await getPlayer(userId);
                    if (!player) { throw new Error("Get Player Data Failed!"); }

                    player.tableId = "";

                    player = await updatePlayer(player);
                    if (!player) { throw new Error("Update Player Data Failed!"); }

                    Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                        sentId: player.socketId,
                        data: {
                            error: true,
                            isTicket: false,
                            message: CONSTANTS.ERROR.ENTRYFEE_INSUFFICIANT_BALANCE
                        },
                        message: `MinPlayer EntryFee Not Deducted !`,
                    });

                    // ! Game Is AllMost Complete API Call
                    await markCompletedGameStatus(
                        {
                            tableId: data.tableId,
                            gameId: player.gameId,
                            tournamentId: player.lobbyId,
                        },
                        player.token,
                        player.socketId,
                        player.projectType
                    );

                    const socket = io.sockets.sockets.get(player.socketId);
                    if (socket) { socket.leave(data.tableId); }

                    await removeDataPlayer(userId, data.tableId);

                }

                await removeOnlyTable(data.tableId);

                for (let j = 0; j < multiPlayerDeductEntryFeeData.deductedUserIds.length; j++) {
                    const userId = multiPlayerDeductEntryFeeData.deductedUserIds[j];

                    let player = await getPlayer(userId);
                    if (!player) { throw new Error("Get Player Data Failed!"); }

                    // ! Game Is AllMost Complete API Call
                    await markCompletedGameStatus(
                        {
                            tableId: data.tableId,
                            gameId: player.gameId,
                            tournamentId: player.lobbyId,
                        },
                        player.token,
                        player.socketId,
                        player.projectType
                    );

                    const socket = io.sockets.sockets.get(player.socketId);
                    if (socket) {

                        socket.leave(player.tableId);

                        player.tableId = "";

                        player = await updatePlayer(player);
                        if (!player) { throw new Error("Update Player Data Failed!"); }

                        const Tabledata: I_MATCH_MAKING = {
                            entryFee: player.entryFee,
                            ticket: player.ticket,
                            userId
                        }
                        await matchMaking(Tabledata, socket);

                    } else {

                        let player = await getPlayer(userId);
                        if (!player) { throw new Error("Get Player Data Failed!"); }

                        player.tableId = "";

                        player = await updatePlayer(player);
                        if (!player) { throw new Error("Update Player Data Failed!"); }

                        Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                            sentId: player.socketId,
                            data: {
                                error: true,
                                isTicket: false,
                                message: CONSTANTS.ERROR.ENTRYFEE_NOT_MINIMUM_PLAYER
                            },
                            message: `EntryFee API Response Insufficiant Balance !`,
                        });

                        await removeDataPlayer(userId, data.tableId);
                    }
                }

            } else {

                Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                    sentId: data.tableId,
                    data: {
                        error: true,
                        isTicket: false,
                        message: CONSTANTS.ERROR.ENTRYFEE_RESPONSE
                    },
                    message: `EntryFee API Response Not Found !`,
                });

                for (let i = 0; i < getTGP.playerList.length; i++) {

                    const userId = getTGP.playerList[i].userId;
                    await removeDataPlayer(userId, data.tableId);

                    let player = await getPlayer(userId);
                    if (player) {
                        const socket = io.sockets.sockets.get(player.socketId);
                        if (socket) { socket.leave(data.tableId); }

                        // ! Game Is AllMost Complete API Call
                        await markCompletedGameStatus(
                            {
                                tableId: data.tableId,
                                gameId: player.gameId,
                                tournamentId: player.lobbyId,
                            },
                            player.token,
                            player.socketId,
                            player.projectType
                        );
                    }
                }

                await removeOnlyTable(data.tableId);
            }

            return false;
        }

        const { isMinPlayerEntryFeeDeducted, deductedUserIds, notDeductedUserIds } = multiPlayerDeductEntryFeeData;
        logger.info(" entryFeeDeductManage :: isMinPlayerEntryFeeDeducted :: >> ", isMinPlayerEntryFeeDeducted);
        logger.info(" entryFeeDeductManage :: deductedUserIds :: >> ", deductedUserIds);
        logger.info(" entryFeeDeductManage ::  notDeductedUserIds :: >> ", notDeductedUserIds);

        if (multiPlayerDeductEntryFeeData.isMinPlayerEntryFeeDeducted && multiPlayerDeductEntryFeeData.notDeductedUserIds.length > 0) {
            logger.info("Api side some users not Deduct EntryFee but  !", "");

            for (let i = 0; i < multiPlayerDeductEntryFeeData.notDeductedUserIds.length; i++) {
                const userId = multiPlayerDeductEntryFeeData.notDeductedUserIds[i];

                let player = await getPlayer(userId);
                if (!player) { throw new Error("Get Player Data Failed!"); }

                player.tableId = "";

                player = await updatePlayer(player);
                if (!player) { throw new Error("Update Player Data Failed!"); }

                Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                    sentId: player.socketId,
                    data: {
                        error: true,
                        isTicket: false,
                        message: CONSTANTS.ERROR.ENTRYFEE_INSUFFICIANT_BALANCE
                    },
                    message: `MinPlayer EntryFee Not Deducted !`,
                });

                // ! Game Is AllMost Complete API Call
                await markCompletedGameStatus(
                    {
                        tableId: data.tableId,
                        gameId: player.gameId,
                        tournamentId: player.lobbyId,
                    },
                    player.token,
                    player.socketId,
                    player.projectType
                );

                const socket = io.sockets.sockets.get(player.socketId);
                if (socket) { socket.leave(data.tableId); }

                await removeDataPlayer(userId, data.tableId);

            }

            await tableLeavePlayerListHandler(data.tableId, multiPlayerDeductEntryFeeData.notDeductedUserIds);

            const gameStartResponse = {
                timer: GAME_START_TIMER,
            }

            Emitter.emit(CONSTANTS.EVENTS.GAME_START, {
                sentId: data.tableId,
                data: gameStartResponse,
                message: CONSTANTS.RESPONSE.GAME_START,
            });

            return true;

        }

        if (!multiPlayerDeductEntryFeeData.isMinPlayerEntryFeeDeducted && multiPlayerDeductEntryFeeData.notDeductedUserIds.length > 0 && multiPlayerDeductEntryFeeData.deductedUserIds.length > 1) {
            logger.info("Api Side EntryFee Deduct Error - 3", "");
            return false;

        }

        if (!multiPlayerDeductEntryFeeData.isMinPlayerEntryFeeDeducted && multiPlayerDeductEntryFeeData.deductedUserIds.length === multiPlayerDeductEntryFeeData.refundedUserIds.length && multiPlayerDeductEntryFeeData.notDeductedUserIds.length === 0) {
            logger.info("Api Side EntryFee Deduct Error - 4", "");
            return false;

        }

        return true;

    } catch (error) {
        console.log('[multiPlayerEntryFeeDeduction] Catch Error :: ', error);
        return false;
    }

}