import { CONSTANTS } from "../../../constants";
import { REQUEST_SIGNUP } from "../../../interface/event";
import logger from "../../../logger";
import { getUserOwnProfile } from "../../clientSideApi";
import { Player, getPlayer, updatePlayer } from "../../database/player";

async function userProfileUpdate(
    userDetail: Player,
    socketId: string,
    signupData: REQUEST_SIGNUP
): Promise<any> {
    try {

        let latitude: string;
        let longitude: string;

        const userOwnProfile = await getUserOwnProfile(
            userDetail.token,
            socketId,
            signupData.projectType
        );

        if (signupData.longitude && signupData.latitude) {
            latitude = signupData.latitude;
            longitude = signupData.longitude;

        } else {

            // latitude, longitude, balance set in user profile
            latitude = userOwnProfile.latitude || "0.0";
            longitude = userOwnProfile.longitude || "0.0";

        }

        const balance: number = signupData.projectType === CONSTANTS.PROJECT_STATE.VM ? userOwnProfile.coins : userOwnProfile.bonus + userOwnProfile.winCash + userOwnProfile.cash || 0;

        let player = await getPlayer(userDetail.userId);
        if (player) {

            player.latitude = latitude;
            player.longitude = longitude;
            player.balance = balance;

            let userId = await updatePlayer(player);

            return userId;
        }

    } catch (error) {
        logger.info("CATCH_ERROR :userSignUp :: ", { userDetail, error });
    }
}

export = userProfileUpdate;
