import { Socket } from "socket.io";
import { CONSTANTS } from "../../../constants";
import { Emitter } from "../../events/eventEmmiter";
import logger from "../../../logger";
import { getPlayerGamePlay, updatePlayerGamePlay } from "../../database/pgp";
import { getTableGamePlay, updateTableGamePlay } from "../../database/table";
import { getPlayer } from "../../database/player";
import { T_TICKET_TYPE } from "../../../interface/common";
import { REQUEST_TICKET_PATTERN } from "../../../interface/event";
import { checkWinner } from "../winner/checkWinner";
import { genratePlayerList, genrateUserList } from "../../helper/genratePlayerList";
import { lostPlayer } from "../winner/lostPlayer";
import { findClaimedPattern } from "../../helper/findClaimed";
import { socketResponseExpectOwn } from "../../events/sockets";
import { getconfig } from "../../../config";


const middleLine = async (
    data: REQUEST_TICKET_PATTERN,
    socket: Socket
) => {

    try {

        const userId = socket.handshake.auth?.id;
        const tableId = socket.handshake.auth?.tableId;
        const { type, ticketNumber } = data;
        let minselectedNumber = false;
        let count = 0;

        let getPGP = await getPlayerGamePlay(userId, tableId);
        if (!getPGP) { throw new Error("get PlayerGamePlay Data Failed!"); }

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        let player = await getPlayer(userId);
        if (!player) { throw new Error("Get Player Data Failed!"); }

        const allreadyClaimed = getPGP.claimed.find((n) => { if (n.type === type && n.ticketNumber === ticketNumber) { return n; } });
        logger.info("[middleLine] allreadyClaimed : ", { allreadyClaimed, ticketNumber: data.ticketNumber });
        if (allreadyClaimed?.ticketNumber !== data.ticketNumber) {

            const findPatternScoreData = getTGP.score.find((s) => { return s.type === type })
            logger.info("[middleLine]findPatternScoreData : ", findPatternScoreData);
            if (!findPatternScoreData) { throw new Error("[middleLine] Find Data Not Found !"); }
            if (findPatternScoreData.count === 0) {
                logger.info("EarlyFive Claim Is Over !", "");
                return;
            }
            for (let i = 0; i < getPGP.ticket.length; i++) {
                const tno = getPGP.ticket[i].ticketNo;
                if (tno === data.ticketNumber && getPGP.ticket[i].selectNumbers.length >= 5) {
                    minselectedNumber = true;
                    for (let j = 0; j < getPGP.ticket[i].ticket[1].length; j++) {
                        const middleLineNumber = getPGP.ticket[i].ticket[1][j];
                        if (middleLineNumber > 0 && getTGP.randomNumbers.includes(middleLineNumber) && getPGP.ticket[i].selectNumbers.includes(middleLineNumber)) {
                            if (findPatternScoreData && findPatternScoreData.count >= 1) { count += 1; }
                        }
                    }
                }
            }
            if (!minselectedNumber) {
                logger.info("[middleLine] Minimum 5 Number Select, You Lost Game .", "")

                await lostPlayer(userId, tableId, socket, CONSTANTS.GAME.MIDDLE_LINE, CONSTANTS.ERROR.MIDDLELINE_ERROR, ticketNumber);
                return;
            }

            if (count === 5) {

                getPGP.tickitScore += findPatternScoreData.score;
                getPGP.totalclaimedMoney += getTGP.score[2].price;
                getPGP.claimed.push({
                    ticketNumber: data.ticketNumber,
                    type: type as T_TICKET_TYPE
                });

                getPGP = await updatePlayerGamePlay(getPGP);
                if (!getPGP) { throw new Error("Update PlayerGamePlay Data Failed!"); }

                if (findPatternScoreData.count === 1) { findPatternScoreData.claimed = true; }
                else { findPatternScoreData.claimed = false; }
                findPatternScoreData.count -= 1;

                getTGP = await updateTableGamePlay(getTGP);
                if (!getTGP) { throw new Error("Get TableGamePlay Data Failed!"); }

                getTGP.claims += 1;
                getTGP.scoreList.push({
                    userId,
                    userName: player.userName,
                    userProfile: player.userProfile,
                    score: findPatternScoreData.score,
                    price: getTGP.score[2].price,
                    type: type as T_TICKET_TYPE
                });

                getTGP = await updateTableGamePlay(getTGP);
                if (!getTGP) { throw new Error("Get TableGamePlay Data Failed!"); }

                const responseData = await findClaimedPattern(ticketNumber, tableId, userId, false);
                if (!responseData) { throw new Error("[MiddleLine] find Claimed Pattern Failed!"); }

                Emitter.emit(CONSTANTS.EVENTS.PATTERN, {
                    sentId: player.socketId,
                    data: responseData,
                    message: CONSTANTS.RESPONSE.MIDDLE_LINE_SUCCESS,
                });

                const extractedData = responseData.pattern.map(({ count, type }) => ({ count, type }));

                await socketResponseExpectOwn(
                    { pattern: extractedData, claim: getTGP.claims },
                    tableId,
                    CONSTANTS.EVENTS.CLAIM_COUNT,
                    CONSTANTS.RESPONSE.MIDDLE_LINE_SUCCESS,
                    socket
                );

            } else {
                logger.info("[MiddleLine] ball number is not present in the board !", "");

                await lostPlayer(userId, tableId, socket, CONSTANTS.GAME.MIDDLE_LINE, CONSTANTS.ERROR.MIDDLELINE_ERROR_2, ticketNumber);
                return;
            }


            const playerList = await genrateUserList(tableId, 4, userId);
            if (!playerList) { throw new Error("[MiddleLine] playerList Not Found !"); }
            if (playerList) {
                Emitter.emit(CONSTANTS.EVENTS.USERLIST, {
                    sentId: tableId,
                    data: { userList: playerList, isRejoin: false },
                    message: CONSTANTS.RESPONSE.USERLIST_SUCCESS,
                });
            }

        }

        getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        const hasAvailablecounts = getTGP.score.some((s) => s.count > 0);
        if (!hasAvailablecounts) {
            logger.info("Calling Winning Functions, NO AVAILABLE COUNTS !!","");
            await checkWinner(tableId, userId, false);
        }

        return;

    } catch (error) {
        logger.info('[middleLine] Error >> ', error);
        Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
            sentId: socket.id,
            data: {
                error: true,
                message: `${error}`
            },
            message: `middleLine Function Error !`,
        });

    }
}

export { middleLine };