import { Socket } from "socket.io";
import { Emitter } from "../../events/eventEmmiter";
import { CONSTANTS } from "../../../constants";
import logger from "../../../logger";
import { REQUEST_TICKET_PATTERN } from "../../../interface/event";
import { getTableGamePlay, updateTableGamePlay } from "../../database/table";
import { getPlayerGamePlay, updatePlayerGamePlay } from "../../database/pgp";
import { getPlayer } from "../../database/player";
import { T_TICKET_TYPE } from "../../../interface/common";
import { checkWinner } from "../winner/checkWinner";
import { genratePlayerList, genrateUserList } from "../../helper/genratePlayerList";
import { lostPlayer } from "../winner/lostPlayer";
import { findClaimedPattern } from "../../helper/findClaimed";
import { socketResponseExpectOwn } from "../../events/sockets";
import { getconfig } from "../../../config";


const corners = async (
    data: REQUEST_TICKET_PATTERN,
    socket: Socket
) => {

    try {
        const userId = socket.handshake.auth?.id;
        const tableId = socket.handshake.auth?.tableId;
        const { type, ticketNumber } = data;
        let count = 0;

        let getPGP = await getPlayerGamePlay(userId, tableId);
        if (!getPGP) { throw new Error("Get PlayerGamePlay Data Failed!"); }

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        let player = await getPlayer(userId);
        if (!player) { throw new Error("Get Player Data Failed!"); }

        const allreadyClaimed = getPGP.claimed.find((n) => { if (n.type === type && n.ticketNumber === ticketNumber) { return n; } });
        if (allreadyClaimed?.ticketNumber !== data.ticketNumber) {

            const findPatternScoreData = getTGP.score.find((s) => { return s.type === type });
            if (!findPatternScoreData) { throw new Error("[corners] Find Data Not Found !"); }
            if (findPatternScoreData.count === 0) {
                logger.info("Corners Claim Is Over !", "");
                return;
            }

            const findTicket = getPGP.ticket.find((n) => { return n.ticketNo === data.ticketNumber && n.selectNumbers.length >= 4 });
            if (!findTicket) {
                logger.info("[LOOSER - CORNERS NOT CLAIMED] , Minimum 4 Number Not Selected !", "");

                await lostPlayer(userId, tableId, socket, CONSTANTS.GAME.CORNERS, CONSTANTS.ERROR.CORNERS_ERROR, ticketNumber);
                return;
            }
            if (findTicket) {

                logger.info("findTicket : ", findTicket);

                for (let i = 0; i < findTicket.ticket[0].length; i++) {
                    const n = findTicket.ticket[0][i];
                    logger.info("n : ", n);
                    if (n > 0) {
                        if (findTicket.selectNumbers.length >= 4 && findTicket.selectNumbers.includes(n) && getTGP.randomNumbers.includes(n)) {
                            count += 1;
                        }
                        break;
                    }
                }

                for (let i = findTicket.ticket[0].length; i > 0; i--) {
                    const n = findTicket.ticket[0][i];
                    if (n > 0) {
                        if (findTicket.selectNumbers.length >= 4 && findTicket.selectNumbers.includes(n) && getTGP.randomNumbers.includes(n)) {
                            count += 1;
                        }
                        break;
                    }
                }

                for (let i = 0; i < findTicket.ticket[2].length; i++) {
                    const n = findTicket.ticket[2][i];
                    if (n > 0) {
                        if (findTicket.selectNumbers.length >= 4 && findTicket.selectNumbers.includes(n) && getTGP.randomNumbers.includes(n)) {
                            count += 1;
                        }
                        break;
                    }
                }

                for (let i = findTicket.ticket[2].length; i > 0; i--) {
                    const n = findTicket.ticket[2][i];
                    if (n > 0) {
                        if (findTicket.selectNumbers.length >= 4 && findTicket.selectNumbers.includes(n) && getTGP.randomNumbers.includes(n)) {
                            count += 1;
                        }
                        break;
                    }
                }

            }

            if (count === 4) {

                getPGP.tickitScore += findPatternScoreData.score;
                logger.info("waitingTable[0] corners : ", getTGP.waitingTable[0].corners);
                logger.info("Config CORNERS_PERSON : ", getconfig().GAMEPLAY.CORNERS_PERSON);
                getPGP.totalclaimedMoney += getTGP.score[4].price;
                logger.info("totalclaimedMoney : ", getPGP.totalclaimedMoney);
                getPGP.claimed.push({
                    ticketNumber: data.ticketNumber,
                    type: type as T_TICKET_TYPE
                });

                getPGP = await updatePlayerGamePlay(getPGP);
                if (!getPGP) { throw new Error("Update PlayerGamePlay Data Failed!"); }

                logger.info("WINNER - CORNERS CLAIMED","");
                if (findPatternScoreData.count === 1) { findPatternScoreData.claimed = true; }
                else { findPatternScoreData.claimed = false; }
                findPatternScoreData.count -= 1;

                getTGP = await updateTableGamePlay(getTGP);
                if (!getTGP) { throw new Error("Get TableGamePlay Data Failed!"); }

                getTGP.claims += 1;
                getTGP.scoreList.push({
                    userId,
                    userName: player.userName,
                    userProfile: player.userProfile,
                    score: findPatternScoreData.score,
                    price: getTGP.score[4].price,
                    type: type as T_TICKET_TYPE
                });

                getTGP = await updateTableGamePlay(getTGP);
                if (!getTGP) { throw new Error("Get TableGamePlay Data Failed!"); }

                const responseData = await findClaimedPattern(ticketNumber, tableId, userId, false);
                if (!responseData) { throw new Error("[corners] find Claimed Pattern Failed!"); }

                Emitter.emit(CONSTANTS.EVENTS.PATTERN, {
                    sentId: player.socketId,
                    data: responseData,
                    message: CONSTANTS.RESPONSE.CORNERS_SUCCESS,
                });

                const extractedData = responseData.pattern.map(({ count, type }) => ({ count, type }));

                await socketResponseExpectOwn(
                    { pattern: extractedData, claim: getTGP.claims },
                    tableId,
                    CONSTANTS.EVENTS.CLAIM_COUNT,
                    CONSTANTS.RESPONSE.CORNERS_SUCCESS,
                    socket
                );

            }
            else {
                logger.info("LOOSER - CORNERS NOT CLAIMED", "");
                await lostPlayer(userId, tableId, socket, CONSTANTS.GAME.CORNERS, CONSTANTS.ERROR.CORNERS_ERROR_2, ticketNumber);
                return;
            }

            const playerList = await genrateUserList(tableId, 4, userId);
            if (!playerList) { throw new Error("[corners] PlayerList Not Found !"); }
            if (playerList) {
                Emitter.emit(CONSTANTS.EVENTS.USERLIST, {
                    sentId: tableId,
                    data: { userList: playerList, isRejoin: false },
                    message: CONSTANTS.RESPONSE.USERLIST_SUCCESS,
                });
            }

        }

        getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        const hasAvailablecounts = getTGP.score.some((s) => s.count > 0);
        if (!hasAvailablecounts) {
            logger.info("Calling Winning Functions, NO AVAILABLE COUNTS !!","");
            await checkWinner(tableId, userId, false);
        }

        return;

    } catch (error) {
        logger.info('[corners] Error >> ', error);
        Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
            sentId: socket.id,
            data: {
                error: true,
                message: `${error}`
            },
            message: `corners Function Error !`,
        });

    }
}

export { corners };