import { Socket } from "socket.io";
import { Emitter } from "../../events/eventEmmiter";
import { CONSTANTS } from "../../../constants";
import logger from "../../../logger";
import { REQUEST_TICKET_PATTERN } from "../../../interface/event";
import { getTableGamePlay, updateTableGamePlay } from "../../database/table";
import { getPlayerGamePlay, updatePlayerGamePlay } from "../../database/pgp";
import { getPlayer } from "../../database/player";
import { T_TICKET_TYPE } from "../../../interface/common";
import { checkWinner } from "../winner/checkWinner";
import { genratePlayerList, genrateUserList } from "../../helper/genratePlayerList";
import { lostPlayer } from "../winner/lostPlayer";
import { findClaimedPattern } from "../../helper/findClaimed";
import { socketResponseExpectOwn } from "../../events/sockets";
import { getconfig } from "../../../config";


const fullHouse = async (
    data: REQUEST_TICKET_PATTERN,
    socket: Socket
) => {

    try {
        const userId = socket.handshake.auth?.id;
        const tableId = socket.handshake.auth?.tableId;
        const { type, ticketNumber } = data;
        let count = 0;

        let getPGP = await getPlayerGamePlay(userId, tableId);
        if (!getPGP) { throw new Error("Get PlayerGamePlay Data Failed!"); }

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        let player = await getPlayer(userId);
        if (!player) { throw new Error("Get Player Data Failed!"); }

        const allreadyClaimed = getPGP.claimed.find((n) => { if (n.type === type && n.ticketNumber === ticketNumber) { return n; } });
        if (allreadyClaimed?.ticketNumber !== data.ticketNumber) {

            const findPatternScoreData = getTGP.score.find((s) => { return s.type === type });
            if (!findPatternScoreData) { throw new Error("[fullHouse] Find Data Not Found !"); }
            if (findPatternScoreData.count === 0) {
                logger.info("FullHouse Claim Is Over !", "");
                return;
            }

            const findTicket = getPGP.ticket.find((n) => { return n.ticketNo === data.ticketNumber && n.selectNumbers.length === 15 })
            if (!findTicket) {
                logger.info("[LOOSER - FullHouse NOT CLAIMED], Minimum 15 Number Not Selected !","");

                await lostPlayer(userId, tableId, socket, CONSTANTS.GAME.FULL_HOUSE, CONSTANTS.ERROR.FULLHOUSE_ERROR, ticketNumber);
                return;
            }
            if (findTicket) {

                const ticket: any = findTicket.ticket.flat(Infinity);

                for (let i = 0; i < ticket.length; i++) {
                    if (ticket[i] > 0 && getTGP.randomNumbers.includes(ticket[i]) && findTicket.selectNumbers.includes(ticket[i])) {
                        count += 1;
                    }
                }

                if (count === 15) {

                    logger.info("WINNER - FULL_HOUSE CLAIMED", "");

                    getPGP.tickitScore += findPatternScoreData.score;
                    getPGP.totalclaimedMoney += getTGP.score[5].price;
                    getPGP.claimed.push({
                        ticketNumber: data.ticketNumber,
                        type: type as T_TICKET_TYPE
                    })
                    getPGP = await updatePlayerGamePlay(getPGP);
                    if (!getPGP) { throw new Error("Update PlayerGamePlay Data Failed!"); }

                    findPatternScoreData.claimed = true;
                    findPatternScoreData.count -= 1;

                    getTGP = await updateTableGamePlay(getTGP);
                    if (!getTGP) { throw new Error("Get TableGamePlay Data Failed!"); }

                    getTGP.claims += 1;
                    getTGP.scoreList.push({
                        userId,
                        userName: player.userName,
                        userProfile: player.userProfile,
                        score: findPatternScoreData.score,
                        price: getTGP.score[5].price,
                        type: type as T_TICKET_TYPE
                    });

                    getTGP = await updateTableGamePlay(getTGP);
                    if (!getTGP) { throw new Error("Get TableGamePlay Data Failed!"); }

                    const responseData = await findClaimedPattern(ticketNumber, tableId, userId, false);
                    if (!responseData) { throw new Error("[fullHouse] find Claimed Pattern Failed!"); }

                    Emitter.emit(CONSTANTS.EVENTS.PATTERN, {
                        sentId: player.socketId,
                        data: responseData,
                        message: CONSTANTS.RESPONSE.FULL_HOUSE_SUCCESS,
                    });

                    const extractedData = responseData.pattern.map(({ count, type }) => ({ count, type }));

                    await socketResponseExpectOwn(
                        { pattern: extractedData, claim: getTGP.claims },
                        tableId,
                        CONSTANTS.EVENTS.CLAIM_COUNT,
                        CONSTANTS.RESPONSE.FULL_HOUSE_SUCCESS,
                        socket
                    );

                } else {
                    logger.info("LOOSER - FULL_HOUSE NOT CLAIMED", "");

                    await lostPlayer(userId, tableId, socket, CONSTANTS.GAME.FULL_HOUSE, CONSTANTS.ERROR.FULLHOUSE_ERROR_2, ticketNumber);
                    return;
                }

                const playerList = await genrateUserList(tableId, 4, userId);
                if (!playerList) { throw new Error("[FullHouse] playerList Not Found !"); }
                if (playerList) {
                    Emitter.emit(CONSTANTS.EVENTS.USERLIST, {
                        sentId: tableId,
                        data: { userList: playerList, isRejoin: false },
                        message: CONSTANTS.RESPONSE.USERLIST_SUCCESS,
                    });
                }
            }
        }

        getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        const hasAvailablecounts = getTGP.score.some((s) => s.count > 0);
        if (!hasAvailablecounts) {
            logger.info("Calling Winning Functions , NO AVAILABLE COUNTS !!","");
            await checkWinner(tableId, userId, false);
        }

        return;

    } catch (error) {
        logger.info('[fullHouse] Error >> ', error);
        Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
            sentId: socket.id,
            data: {
                error: true,
                message: `${error}`
            },
            message: `fullHouse Function Error !`,
        });

    }

}

export { fullHouse };