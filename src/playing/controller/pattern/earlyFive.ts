import { Socket } from "socket.io";
import { Emitter } from "../../events/eventEmmiter";
import { CONSTANTS } from "../../../constants";
import logger from "../../../logger";
import { REQUEST_TICKET_PATTERN } from "../../../interface/event";
import { getTableGamePlay, updateTableGamePlay } from "../../database/table";
import { getPlayerGamePlay, updatePlayerGamePlay } from "../../database/pgp";
import { getPlayer } from "../../database/player";
import { checkWinner } from "../winner/checkWinner";
import { genratePlayerList, genrateUserList } from "../../helper/genratePlayerList";
import { lostPlayer } from "../winner/lostPlayer";
import { findClaimedPattern } from "../../helper/findClaimed";
import { socketResponseExpectOwn } from "../../events/sockets";
import { getconfig } from "../../../config";


const earlyFive = async (
    data: REQUEST_TICKET_PATTERN,
    socket: Socket
) => {

    logger.info("EARLY FIVE : ", data);

    try {

        const userId = socket.handshake.auth?.id;
        const tableId = socket.handshake.auth?.tableId;
        const { type, ticketNumber } = data;
        let count = 0;

        let getPGP = await getPlayerGamePlay(userId, tableId);
        if (!getPGP) { throw new Error("get PlayerGamePlay Data Failed!"); }

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        let player = await getPlayer(userId);
        if (!player) { throw new Error("Get Player Data Failed!"); }

        logger.info("Total Number Balls : ", getTGP.totalNumbers);
        logger.info("Random Number Balls : ", getTGP.randomNumbers);

        const allreadyClaimed = getPGP.claimed.find((n: any) => {
            if (n.type === type && n.ticketNumber === ticketNumber) { return n; }
        });
        logger.info("[EarlyFive] allreadyClaimed : ", allreadyClaimed);
        logger.info("[EarlyFive] ticketNumber : ", data.ticketNumber);
        if (allreadyClaimed?.ticketNumber !== data.ticketNumber) {

            const findPatternScoreData = getTGP.score.find((s) => { return s.type === type });
            logger.info("findPatternScoreData : ", findPatternScoreData);
            if (!findPatternScoreData) { throw new Error("[EarlyFive] Find Data Not Found !"); }
            if (findPatternScoreData.count === 0) {
                logger.info("EarlyFive Claim Is Over !", "");
                return;
            }

            const findTicket = getPGP.ticket.find((n) => { return n.ticketNo === data.ticketNumber && n.selectNumbers.length >= 5 })
            logger.info("findTicket : ", findTicket)
            if (!findTicket) {
                logger.info("[LOOSER - EarlyFive NOT CLAIMED] , Minimum 5 Number Not Selected !","");

                await lostPlayer(userId, tableId, socket, CONSTANTS.GAME.EARLY_FIVE, CONSTANTS.ERROR.EARLYFIVE_ERROR, ticketNumber);
                return;
            }
            if (findTicket) {

                const ticket: any = findTicket.ticket.flat(Infinity);

                logger.info("ticket : ", ticket)

                for (let i = 0; i < ticket.length; i++) {
                    if (ticket[i] > 0 && getTGP.randomNumbers.includes(ticket[i]) && findTicket.selectNumbers.includes(ticket[i])) {
                        count += 1;
                    }
                }

                logger.info("[EarlyFive] Count : ", count)

                if (count >= 5) {

                    logger.info("WINNER - EARLYFIVE CLAIMED", "");

                    getPGP.tickitScore += findPatternScoreData.score;
                    getPGP.totalclaimedMoney += getTGP.score[0].price;
                    getPGP.claimed.push({
                        ticketNumber: data.ticketNumber,
                        type: type
                    });

                    getPGP = await updatePlayerGamePlay(getPGP);
                    if (!getPGP) { throw new Error("Update PlayerGamePlay Data Failed!"); }

                    findPatternScoreData.claimed = false;
                    findPatternScoreData.count -= 1;

                    getTGP = await updateTableGamePlay(getTGP);
                    if (!getTGP) { throw new Error("Update Table Game Play Data Failed!"); }

                    getTGP.claims += 1;
                    getTGP.scoreList.push({
                        userId,
                        userName: player.userName,
                        userProfile: player.userProfile,
                        score: findPatternScoreData.score,
                        price: getTGP.score[0].price,
                        type: type
                    });

                    getTGP = await updateTableGamePlay(getTGP);
                    if (!getTGP) { throw new Error("Update Table Game Play Data Failed!"); }

                    const responseData = await findClaimedPattern(ticketNumber, tableId, userId, false);
                    if (!responseData) { throw new Error("[EarlyFive] find Claimed Pattern Failed!"); }

                    Emitter.emit(CONSTANTS.EVENTS.PATTERN, {
                        sentId: player.socketId,
                        data: responseData,
                        message: CONSTANTS.RESPONSE.EARLY_FIVE_SUCCESS,
                    });

                    const extractedData = responseData.pattern.map(({ count, type }) => ({ count, type }));

                    await socketResponseExpectOwn(
                        { pattern: extractedData, claim: getTGP.claims },
                        tableId,
                        CONSTANTS.EVENTS.CLAIM_COUNT,
                        CONSTANTS.RESPONSE.EARLY_FIVE_SUCCESS,
                        socket
                    );

                }
                else {
                    logger.info("[LOOSER - EarlyFive NOT CLAIMED] , Please Select Valid 5 Numbers !", count);
                    await lostPlayer(userId, tableId, socket, CONSTANTS.GAME.EARLY_FIVE, CONSTANTS.ERROR.EARLYFIVE_ERROR, ticketNumber);
                    return;
                }
            }

            const playerList = await genrateUserList(tableId, 4, userId);
            if (!playerList) { throw new Error("[EarlyFive] playerList Not Found !"); }
            if (playerList) {

                Emitter.emit(CONSTANTS.EVENTS.USERLIST, {
                    sentId: tableId,
                    data: { userList: playerList, isRejoin: false },
                    message: CONSTANTS.RESPONSE.USERLIST_SUCCESS,
                });

            }

        }
        else {
            logger.info("[LOOSER - EARLYFIVE NOT CLAIMED] Valid Number Not Selected !","");

            await lostPlayer(userId, tableId, socket, CONSTANTS.GAME.EARLY_FIVE, CONSTANTS.ERROR.EARLYFIVE_ERROR_2, ticketNumber);
            return;
        }

        const hasAvailablecounts = getTGP.score.some((s) => s.count > 0);
        if (!hasAvailablecounts) {
            logger.info("Calling Winning Functions, NO AVAILABLE COUNTS !!","");
            await checkWinner(tableId, userId, false);
        }

        return;

    } catch (error) {
        logger.info('[EarlyFive] Error >> ', error);
        Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
            sentId: socket.id,
            data: {
                error: true,
                message: `${error}`
            },
            message: `earlyFive Function Error !`,
        });

    }

}

export { earlyFive };