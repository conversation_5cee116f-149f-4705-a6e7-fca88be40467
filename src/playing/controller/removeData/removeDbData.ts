import Bull from "bull";
import { disconnectTimer, gameStartTimer, matchMakingTimer, randomBallTimer } from "../../../bull/allJobs";
import { CONSTANTS } from "../../../constants";
import logger from "../../../logger";
import { getOnlineUser, removeOnlineUser, removeOnlineUserLobbyWise } from "../../database/onlineStatus";
import { REDIS_DELETE } from "../../database/redis";
import { getTableGamePlay, updateTableGamePlay } from "../../database/table";
import { getPlayer, updatePlayer } from "../../database/player";
import { getPlayerGamePlay } from "../../database/pgp";


export const removeData = async (
    userId: string,
    tableId: string,
) => {

    try {
        logger.info("IN removeData : ", { userId, tableId });

        const Key: Bull.JobId = `${CONSTANTS.BULL.MATCHMAKING_BULL}:${tableId}`;
        const matchMakingBull = await matchMakingTimer.getJob(Key);
        if (matchMakingBull) { matchMakingBull.remove(); }

        const jobKey: Bull.JobId = `${CONSTANTS.BULL.GAME_START_BULL}:${tableId}`;
        const gameStartBull = await gameStartTimer.getJob(jobKey);
        if (gameStartBull) { gameStartBull.remove(); }

        const jobKey2: Bull.JobId = `${CONSTANTS.BULL.RANDOM_BALL_BULL}:${tableId}`;
        const randomBallBull = await randomBallTimer.getJob(jobKey2);
        if (randomBallBull) { randomBallBull.remove(); }

        await removeAllPGP(tableId);

        const key2 = `${CONSTANTS.REDIS.TABLE_GAME_PLAY}:${tableId}`;
        await REDIS_DELETE(key2);

    } catch (error) {
        logger.info("[removeData] Catch Error : ", error);
    }

}

export const removeDataPlayer = async (
    userId: string,
    tableId: string,
) => {

    try {
        logger.info("IN removeDataPlayer : ", { userId, tableId });

        const onlineUserData = await getOnlineUser(userId);
        if (onlineUserData) {
            await removeOnlineUser(userId);
            await removeOnlineUserLobbyWise(userId, onlineUserData.lobbyId);
        }

        const key = `${CONSTANTS.REDIS.PLAYER_GAME_PLAY}:${userId}:${tableId}`;
        await REDIS_DELETE(key);

    } catch (error) {
        logger.info("[removeDataPlayer] Catch Error : ", error);
    }

}


export const removeOnlyTable = async (
    tableId: string,
) => {

    try {
        logger.info("IN removeOnlyTable : ", { tableId });

        const key = `${CONSTANTS.REDIS.TABLE_GAME_PLAY}:${tableId}`;
        await REDIS_DELETE(key);

    } catch (error) {
        logger.info("[removeOnlyTable] Catch Error : ", error);
    }

}

export const removeAllPGP = async (
    tableId: string,
) => {

    try {
        logger.info("IN removeAllPGP : ", { tableId });

        let getTGP = await getTableGamePlay(tableId);
        if (getTGP) {

            for (let i = 0; i < getTGP.playerList.length; i++) {
                const userId = getTGP.playerList[i].userId;

                let getPGP = await getPlayerGamePlay(userId,tableId);

                const jobKeys: Bull.JobId = `${CONSTANTS.BULL.DISCONNECT_BULL}:${userId}:${tableId}`;
                const disconnectBull = await disconnectTimer.getJob(jobKeys);
                if (disconnectBull) { disconnectBull.remove(); }

                let player = await getPlayer(userId);
                if (player && getPGP?.playerState !== CONSTANTS.STATE.DISCONNECT) {
                    player.tableId = "";
                    await updatePlayer(player);
                }

                const key = `${CONSTANTS.REDIS.PLAYER_GAME_PLAY}:${userId}:${tableId}`;
                await REDIS_DELETE(key);

                const onlineUserData = await getOnlineUser(userId);
                if (onlineUserData) {
                    await removeOnlineUser(userId);
                    await removeOnlineUserLobbyWise(userId, onlineUserData.lobbyId);
                }

            }
        }
        return;

    } catch (error) {
        logger.info("[removeAllPGP] Catch Error : ", error);
    }

}


export const tableLeavePlayerListHandler = async (
    tableId: string,
    notDeductedUserIds: string[]
): Promise<void> => {

    try {

        let table = await getTableGamePlay(tableId);
        if (!table) { throw new Error("Get Table Game Play Data Failed!"); }

        const updatedPlayerList = table.playerList.filter((player) => !notDeductedUserIds.includes(player.userId));

        table.playerList = updatedPlayerList;

        await updateTableGamePlay(table);

    } catch (error) {
        logger.info("[tableLeavePlayerListHandler] Catch Error : ", error);
    }

}