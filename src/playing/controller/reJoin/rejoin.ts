import { Socket } from "socket.io";
import { I_REJOIN, I_WINNING_SCREEN } from "../../../interface/common";
import logger from "../../../logger";
import { CONSTANTS } from "../../../constants";
import { Emitter } from "../../events/eventEmmiter";
import { getPlayerGamePlay, updatePlayerGamePlay } from "../../database/pgp";
import { getTableGamePlay, updateTableGamePlay } from "../../database/table";
import { getPlayer } from "../../database/player";
import { getconfig } from "../../../config";
import { remainingTimeCalculation } from "../../helper/remainingTime";
import { disconnectTimer, gameStartTimer, matchMakingTimer, randomBallTimer } from "../../../bull/allJobs";
import Bull from "bull";
import { genratePlayerList } from "../../helper/genratePlayerList";
import { winnerResponsefinder } from "../../helper/scoreFinder";
import { findClaimedPattern } from "../../helper/findClaimed";



export const rejoin = async (
    data: I_REJOIN,
    socket: Socket
): Promise<boolean> => {

    try {

        const { userId, tableId } = data;
        const { RANDOM_BALL_TIMER } = getconfig().GAMEPLAY;

        socket.join(tableId);

        const jobKeys: Bull.JobId = `${CONSTANTS.BULL.DISCONNECT_BULL}:${userId}:${tableId}`;
        const disconnectBull = await disconnectTimer.getJob(jobKeys);
        if (disconnectBull) { disconnectBull.remove(); }

        let getPGP = await getPlayerGamePlay(userId, tableId);
        if (!getPGP) { throw new Error("get PlayerGamePlay Data Failed!"); }

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        let player = await getPlayer(userId);
        if (!player) { throw new Error("Get Player Data Failed!"); }

        const jobKey: Bull.JobId = `${CONSTANTS.BULL.RANDOM_BALL_BULL}:${tableId}`;
        const gamejob = await randomBallTimer.getJob(jobKey);
        const ballRemainingTime = await remainingTimeCalculation(gamejob);

        const jobKey2: Bull.JobId = `${CONSTANTS.BULL.GAME_START_BULL}:${tableId}`;
        const balljob = await gameStartTimer.getJob(jobKey2);
        const gameRemainingTime = await remainingTimeCalculation(balljob);

        const jobKey4: Bull.JobId = `${CONSTANTS.BULL.MATCHMAKING_BULL}:${tableId}`;
        const matchMakingJob = await matchMakingTimer.getJob(jobKey4);
        const matchMakingRemainingTime = await remainingTimeCalculation(matchMakingJob);

        getPGP.playerState = CONSTANTS.STATE.PLAYING;

        getPGP = await updatePlayerGamePlay(getPGP);
        if (!getPGP) { throw new Error("Update PlayerGamePlay Failed !!"); }

        if (getTGP.waitingTable.length > 0) {
            getTGP.waitingTable[0].remaining_Time = matchMakingRemainingTime;
        }
        if (getTGP.colorBall.length > 0) {
            getTGP.colorBall[0].timer = ballRemainingTime;
        }

        getTGP = await updateTableGamePlay(getTGP);
        if (!getTGP) { throw new Error("Update TableGamePlay Data Failed !"); }

        const responseData = await findClaimedPattern(1, tableId, userId, false);

        if (!responseData) { throw new Error("[REJOIN] findClaimedPattern Failed !"); }

        const rejoinResponse = {
            state: getTGP.tableState,
            gameStartTimer: gameRemainingTime,
            claims: getTGP.claims,
            totalTickets: getTGP.total_ticket,
            allDigit: getTGP.randomNumbers,
            randomNumbers: getTGP.colorBall.length ? getTGP.colorBall : [],
            timer: RANDOM_BALL_TIMER,
            tickets: getPGP.ticket,
            pattern: responseData.pattern,
            isClaimTouch: responseData.isClaimTouch,
            waitingTable: getTGP.waitingTable.length ? getTGP.waitingTable[0] : {},
        }

        Emitter.emit(CONSTANTS.EVENTS.RE_JOIN, {
            sentId: socket.id,
            data: rejoinResponse,
            message: CONSTANTS.RESPONSE.REJOIN_SUCCESS,
        });

        Emitter.emit(CONSTANTS.EVENTS.USERLIST, {
            sentId: tableId,
            data: { userList: getTGP.userList, isRejoin: true },
            message: CONSTANTS.RESPONSE.USERLIST_SUCCESS,
        });

        if (getPGP.isLost) {

            Emitter.emit(CONSTANTS.EVENTS.ERROR_TICKET, {
                sentId: socket.id,
                data: { invalidTicket: getPGP.lostList },
                message: `Pattern Function Error !`,
            });

        }

        if (getTGP.tableState === CONSTANTS.STATE.WIN && !getPGP.isLost) {

            const WinnerResponse = await winnerResponsefinder(tableId, false);
            if (!WinnerResponse) { throw new Error("Winner Response finder Failed!"); }

            const updatedWinner = WinnerResponse.map((user) => {
                if (user.userId === userId) { return { ...user, you: true }; }
                return user;
            });

            const responseData: I_WINNING_SCREEN = {
                winner: updatedWinner,
            }

            Emitter.emit(CONSTANTS.EVENTS.WINNING, {
                sentId: player.socketId,
                data: responseData,
                message: CONSTANTS.RESPONSE.WINNING_SUCCESS,
            });

        }

        return true;

    } catch (error) {
        console.log('[rejoin] Error >> ', error);
        return false;
    }

}