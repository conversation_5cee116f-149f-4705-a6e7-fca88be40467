import { Socket } from "socket.io";
import { Lock } from "../../../connection/redlock";
import { CreateNewTable } from "./createTable";
import { joinTable } from "./joinTable";
import { getMatchMaking } from "../../database/redis";
import { getTableGamePlay } from "../../database/table";
import { Emitter } from "../../events/eventEmmiter";
import { CONSTANTS } from "../../../constants";
import { selectTableInputValidtor } from "../../validations";
import logger from "../../../logger";
import { I_MATCH_MAKING } from "../../../interface/common";
import { getconfig } from "../../../config";
import { getPlayer } from "../../database/player";
import { disctanceCalculation } from "../../helper/checkRange";
import { checkUserBlockStatus, rediusCheck } from "../../clientSideApi";
import { addGameRunningStatusIf } from "../../../interface/cmgApiIf";
import { addGameRunningStatus } from "../../clientSideApi/addGameRunningStatus";



const matchMaking = async (data: I_MATCH_MAKING, socket: Socket) => {

    const MatchMakingId = `${CONSTANTS.REDLOCK.LOCK}:${CONSTANTS.REDIS.MATCHMAKING}:${data?.entryFee}`;
    const MatchMakingLock = await Lock.getLock().acquire([MatchMakingId], 2000);

    try {

        const error = await selectTableInputValidtor(data);
        if (error) {
            logger.info("[selectTableInputValidtor] Error : ", error);
            Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                sentId: socket.id,
                data: {
                    error: true,
                    isTicket: false,
                    message: CONSTANTS.ERROR.MATCHMAKING_VALIDATION
                },
                message: `MatchMaking Function Validation Error`,
            });
            throw new Error(CONSTANTS.ERROR.MATCHMAKING_VALIDATION);
        }

        const { entryFee, userId } = data;
        const { MIN_PLAYER } = getconfig().GAMEPLAY;

        let player = await getPlayer(userId);
        if (!player) { throw new Error("Get Player Data Failed !!"); }

        const isMatchMaking = await getMatchMaking(entryFee, MIN_PLAYER, player.ticket);

        logger.info("isMatchMaking : ", isMatchMaking);

        let BlockFromAllUser = true, isRediusValid = true, ticketAvailable = true;

        for (let i = 0; i < isMatchMaking.length; i++) {

            const tableId = isMatchMaking[i].split('"').join('');
            logger.info("MatchMaking tableId :: ", tableId);
            socket.handshake.auth.tableId = tableId;

            let getTGP = await getTableGamePlay(tableId);
            if (!getTGP) { throw new Error("MatchMaking Time Not Found TableGamePlay!"); }
            else {

                let player = await getPlayer(userId);
                if (!player) { throw new Error("Get Player Data Failed!"); }

                if ((getTGP.total_ticket + player.ticket) > 100) {
                    logger.info("Table is Full , So find New Table !", "")
                    ticketAvailable = false;
                }

                const PlayerListExpectRequestUser = getTGP.playerList.filter(({ userId: id }) => id !== userId).map(({ userId }) => userId);

                // ! check User Block Status
                const isUserBlock = await checkUserBlockStatus(PlayerListExpectRequestUser, player.token, socket.id, player.projectType)
                logger.info("isUserBlock :: ", isUserBlock);

                // ! redius (The player which is running under 1/2KM area does not play the game in same Table.)
                const rediusCheckData = await rediusCheck(player.gameId, player.token, socket.id, player.projectType);
                logger.info("rediusCheckData :: ", rediusCheckData);


                if (rediusCheckData) {

                    let locationRangeData: number = parseFloat(rediusCheckData?.LocationRange);

                    logger.info("locationRangeData >>>>>>>>>>>>>>>>>> ", { userId, locationRangeData, });

                    if (rediusCheckData?.isGameRadiusLocationOn && locationRangeData !== 0) {

                        logger.info("rediusCheckData?.isGameRadiusLocationOn && locationRangeData !== 0 >>>>>>>>>>>>>>>>>> ", true);

                        for (let j = 0; j < getTGP.playerList.length; j++) {
                            const otherUserId = getTGP.playerList[j].userId;

                            const OtherPlayersDetails = await getPlayer(otherUserId);
                            if (!OtherPlayersDetails) { throw new Error("get Player Game play not found !"); }

                            const distanceFind = await disctanceCalculation(Number(player.latitude), Number(player.longitude), Number(OtherPlayersDetails.latitude), Number(OtherPlayersDetails.longitude));

                            logger.info("distanceFind >>>>>>>>>>>>>>>>>> ", { userId, distanceFind, j });

                            if (distanceFind < locationRangeData) {

                                isRediusValid = false;
                                break;
                            };

                        }

                    }

                }

                if (!isUserBlock && isRediusValid && ticketAvailable) {

                    logger.info("Temp - Join Table", "");

                    BlockFromAllUser = false;

                    // ! addGameRunningStatus API calls
                    const Gamedata: addGameRunningStatusIf = {
                        tableId: tableId,
                        tournamentId: player.lobbyId,
                        gameId: player.gameId
                    }
                    await addGameRunningStatus(Gamedata, player.token, socket.id, player.userId, player.projectType);

                    // * join Table
                    await joinTable(data, tableId, socket);
                    return;

                }

                if (!BlockFromAllUser) { break; };

            }
        }

        logger.info("[MatchMaking] Why Create Table? :: ", { BlockFromAllUser, ticketAvailable });

        if (BlockFromAllUser || !ticketAvailable) {

            logger.info("temp - create Table", "");
            await CreateNewTable(data, socket);
            return;

        }

    } catch (error: any) {
        logger.info('[matchMaking] Catch Error :: ', error);
        return;

    } finally {

        await Lock.getLock().release(MatchMakingLock);

    }

}

export { matchMaking };