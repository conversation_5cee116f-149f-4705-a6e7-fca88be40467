import { Socket } from "socket.io";
import { I_COLLECTENTRYFEE, I_GAME_START, I_MATCH_MAKING } from "../../../interface/common";
import { getTableGamePlay, updateTableGamePlay } from "../../database/table";
import { getconfig } from "../../../config";
import { getPlayer, updatePlayer } from "../../database/player";
import { CONSTANTS } from "../../../constants";
import { removeValueFromArray } from "../../database/redis";
import { Emitter } from "../../events/eventEmmiter";
import logger from "../../../logger";
import { RESPONSE_JOINTABLE } from "../../../interface/event";
import { remainingTimeCalculation } from "../../helper/remainingTime";
import { matchMakingTimer } from "../../../bull/allJobs";
import Bull from "bull";
import { multiPlayerEntryFeeDeduction } from "../entryFee/entryfeeDeduct";
import { createPlayerGamePlay, getPlayerGamePlay, updatePlayerGamePlay } from "../../database/pgp";
import { gameStartQueue } from "../../../bull/queue/gameStartQueue";
import { genratePlayerList } from "../../helper/genratePlayerList";
import { create_ticket } from "../../helper/createTicket";


const joinTable = async (data: I_MATCH_MAKING, tableId: string, socket: Socket): Promise<void> => {

    try {

        const { entryFee, userId } = data;
        const {
            MAX_PLAYER,
            TOTAL_TICKET,
            FULL_HOUSE_PERCENTAGE,
            TOP_LINE_PERCENTAGE,
            MIDDLE_LINE_PERCENTAGE,
            BOTTOM_LINE_PERCENTAGE,
            EARLY_FIVE_PERCENTAGE,
            CORNERS_PERCENTAGE,
            GAME_START_TIMER,
            FULL_HOUSE_PERSON,
            TOP_LINE_PERSON,
            MIDDLE_LINE_PERSON,
            BOTTOM_LINE_PERSON,
            EARLY_FIVE_PERSON,
            CORNERS_PERSON,
            MATCH_MAKING_TIMER,
            PLATFORM_RAKE
        } = getconfig().GAMEPLAY;
        let userIds: string[] = [];
        let count = 0;

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        let player = await getPlayer(userId);
        if (!player) { throw new Error("Get Player Data Failed!"); }

        let pgp = await createPlayerGamePlay(userId, tableId);
        if (!pgp) { throw new Error("Create PlayerGamePlay Failed !!"); }

        while (count < player.ticket) {
            const ticket = await create_ticket();
            if (ticket) {

                if (!pgp) { throw new Error("get PlayerGamePlay Failed !!"); }

                count += 1;

                pgp.ticket.push({ ticketNo: count, ticket, selectNumbers: [] })

                pgp = await updatePlayerGamePlay(pgp);
                if (!pgp) { throw new Error("update PlayerGamePlay Failed !!"); }

            }
        }

        getTGP.playerList.push({ userId, userName: player.userName, userProfile: player.userProfile, leave: false });
        getTGP.total_ticket = getTGP.total_ticket + player.ticket;

        getTGP = await updateTableGamePlay(getTGP);
        if (!getTGP) { throw new Error("Update Table Data Failed!"); }

        socket.join(tableId);
        socket.handshake.auth.tableId = tableId;

        if (getTGP.playerList.length === MAX_PLAYER || getTGP.total_ticket === TOTAL_TICKET) {

            await removeValueFromArray(entryFee, tableId, player.ticket);

        }

        // const totalAmount = getTGP.total_ticket * player.entryFee; // & for multiple Tickets
        const totalAmount = getTGP.playerList.length * (player.entryFee - (player.entryFee * (PLATFORM_RAKE / 100)));

        const jobKey: Bull.JobId = `${CONSTANTS.BULL.MATCHMAKING_BULL}:${tableId}`;
        const job = await matchMakingTimer.getJob(jobKey);

        const remainingMatchMakiingTime = await remainingTimeCalculation(job);

        const joinTableResponse: RESPONSE_JOINTABLE = {
            total_Time: MATCH_MAKING_TIMER,
            remaining_Time: remainingMatchMakiingTime,
            total_Player: getTGP.playerList.length,
            tickets_Cost: totalAmount,
            total_Tickets: getTGP.total_ticket,
            full_House: Number(((totalAmount * FULL_HOUSE_PERCENTAGE) / 100).toFixed(3)),
            top_Line: Number(((totalAmount * TOP_LINE_PERCENTAGE) / 100).toFixed(3)),
            middle_line: Number(((totalAmount * MIDDLE_LINE_PERCENTAGE) / 100).toFixed(3)),
            bottom_Line: Number(((totalAmount * BOTTOM_LINE_PERCENTAGE) / 100).toFixed(3)),
            early_Five: Number(((totalAmount * EARLY_FIVE_PERCENTAGE) / 100).toFixed(3)),
            corners: Number(((totalAmount * CORNERS_PERCENTAGE) / 100).toFixed(3)),
        }

        getTGP.waitingTable = [joinTableResponse];

        for (let i = 0; i < getTGP.score.length; i++) {
            if (getTGP.score[i].type === CONSTANTS.EVENTS.EARLY_FIVE) { getTGP.score[i].price = Number(((totalAmount * EARLY_FIVE_PERCENTAGE) / (100 * EARLY_FIVE_PERSON)).toFixed(3)) }
            else if (getTGP.score[i].type === CONSTANTS.EVENTS.CORNERS) { getTGP.score[i].price = Number(((totalAmount * CORNERS_PERCENTAGE) / (100 * CORNERS_PERSON)).toFixed(3)) }
            else if (getTGP.score[i].type === CONSTANTS.EVENTS.TOP_LINE) { getTGP.score[i].price = Number(((totalAmount * TOP_LINE_PERCENTAGE) / (100 * TOP_LINE_PERSON)).toFixed(3)) }
            else if (getTGP.score[i].type === CONSTANTS.EVENTS.MIDDLE_LINE) { getTGP.score[i].price = Number(((totalAmount * MIDDLE_LINE_PERCENTAGE) / (100 * MIDDLE_LINE_PERSON)).toFixed(3)) }
            else if (getTGP.score[i].type === CONSTANTS.EVENTS.BOTTOM_LINE) { getTGP.score[i].price = Number(((totalAmount * BOTTOM_LINE_PERCENTAGE) / (100 * BOTTOM_LINE_PERSON)).toFixed(3)) }
            else if (getTGP.score[i].type === CONSTANTS.EVENTS.FULL_HOUSE) { getTGP.score[i].price = Number(((totalAmount * FULL_HOUSE_PERCENTAGE) / (100 * FULL_HOUSE_PERSON)).toFixed(3)) }
        }

        getTGP = await updateTableGamePlay(getTGP);
        if (!getTGP) { throw new Error("Update Table Data Failed!"); }

        player.tableId = tableId;

        player = await updatePlayer(player);
        if (!player) { throw new Error("Update Player Data Failed!"); }

        Emitter.emit(CONSTANTS.EVENTS.JOIN_TABLE, {
            sentId: tableId,
            data: joinTableResponse,
            message: `${tableId}:${CONSTANTS.RESPONSE.UPDATE_JOIN_TABLE}`,
        });

        if (getTGP.playerList.length === MAX_PLAYER || getTGP.total_ticket === TOTAL_TICKET) {

            logger.info("playerList.length === MAX_PLAYER :: ", { playerList_length: getTGP.playerList.length, MAX_PLAYER });

            const playerList = await genratePlayerList(tableId, 4, userId);
            if (!playerList) { throw new Error("[JoinTable] playerList Not Found !"); }

            getTGP.lock = true;
            getTGP.tableState = CONSTANTS.STATE.GAME_START_TIMER;
            getTGP.waitingTable[0].remaining_Time = 0;
            getTGP = await updateTableGamePlay(getTGP);
            if (!getTGP) { throw new Error("Update TableGamePlay Not Found !!"); }

            for (let i = 0; i < getTGP.playerList.length; i++) {

                userIds.push(getTGP.playerList[i].userId);

                let player = await getPlayer(getTGP.playerList[i].userId);
                let getPGP = await getPlayerGamePlay(getTGP.playerList[i].userId, tableId);
                if (player && getPGP) {

                    const selectTableResponse = {
                        tableId: getTGP._id,
                        player: {
                            userId: player.userId,
                            userName: player.userName,
                            userProfile: player.userProfile,
                            score: getPGP.tickitScore,
                            price: Number((getPGP.totalclaimedMoney).toFixed(3))
                        },
                        totalTickets: getTGP.total_ticket,
                        claims: getTGP.claims,
                        winningPatterns: getTGP.score,
                        randomNumbers: getTGP.randomNumbers,
                        ticket: getPGP.ticket,
                    };

                    Emitter.emit(CONSTANTS.EVENTS.SELECT_TABLE, {
                        sentId: player.socketId,
                        data: selectTableResponse,
                        message: CONSTANTS.RESPONSE.CREATE_TABLE,
                    });

                }
            }

            if (playerList) {
                getTGP.userList = playerList;

                getTGP = await updateTableGamePlay(getTGP);
                if (!getTGP) { throw new Error("Update TableGamePlay Not Found !!"); }

                Emitter.emit(CONSTANTS.EVENTS.USERLIST, {
                    sentId: tableId,
                    data: { userList: playerList, isRejoin: false },
                    message: CONSTANTS.RESPONSE.USERLIST_SUCCESS,
                });
            }

            const Bull_data: I_COLLECTENTRYFEE = { tableId, userIds };

            const collectEntryFeeStatus = await multiPlayerEntryFeeDeduction(Bull_data);
            if (collectEntryFeeStatus) {

                const bullData: I_GAME_START = { tableId, userId };

                await gameStartQueue(bullData);

                const gameStartResponse = { timer: GAME_START_TIMER };

                Emitter.emit(CONSTANTS.EVENTS.GAME_START, {
                    sentId: tableId,
                    data: gameStartResponse,
                    message: CONSTANTS.RESPONSE.GAME_START,
                });
            }

        }

        return;

    } catch (error: any) {
        console.log('[joinTable] Catch Error :: ', error);
    }

}

export { joinTable };