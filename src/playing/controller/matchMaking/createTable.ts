import { Socket } from "socket.io";
import { BULL_MATCH_MAKING, I_MATCH_MAKING, I_SCORE } from "../../../interface/common";
import { createTableGamePlay, updateTableGamePlay } from "../../database/table";
import { pushTableInQueue } from "../../database/redis";
import { getPlayer, updatePlayer } from "../../database/player";
import { CONSTANTS } from "../../../constants";
import { Emitter } from "../../events/eventEmmiter";
import { matchMakingTimerQueue } from "../../../bull/queue/matchMakingQueue";
import logger from "../../../logger";
import { getconfig } from "../../../config";
import { RESPONSE_JOINTABLE } from "../../../interface/event";
import { genrateNumbers } from "../../helper";
import { createPlayerGamePlay, updatePlayerGamePlay } from "../../database/pgp";
import { create_ticket } from "../../helper/createTicket";
import { addGameRunningStatusIf } from "../../../interface/cmgApiIf";
import { addGameRunningStatus } from "../../clientSideApi/addGameRunningStatus";


const CreateNewTable = async (data: I_MATCH_MAKING, socket: Socket): Promise<void> => {

    try {

        const { entryFee, userId } = data;
        const {
            FULL_HOUSE_PERCENTAGE,
            TOP_LINE_PERCENTAGE,
            MIDDLE_LINE_PERCENTAGE,
            BOTTOM_LINE_PERCENTAGE,
            EARLY_FIVE_PERCENTAGE,
            CORNERS_PERCENTAGE,
            MATCH_MAKING_TIMER,
            FULL_HOUSE_PERSON,
            TOP_LINE_PERSON,
            MIDDLE_LINE_PERSON,
            BOTTOM_LINE_PERSON,
            EARLY_FIVE_PERSON,
            CORNERS_PERSON,
            PLATFORM_RAKE
        } = getconfig().GAMEPLAY;

        let player = await getPlayer(userId);
        if (!player) { throw new Error("Get Player Data Failed !!"); }

        // const totalAmount = player.ticket * player.entryFee; // & for multiple Tickets
        const totalAmount = player.entryFee - (player.entryFee * (PLATFORM_RAKE / 100));

        const socre: I_SCORE[] = [
            {
                score: CONSTANTS.SCORE.EARLY_FIVE_SCORE,
                price: Number(((totalAmount * EARLY_FIVE_PERCENTAGE) / (100 * EARLY_FIVE_PERSON)).toFixed(3)),
                count: EARLY_FIVE_PERSON,
                type: CONSTANTS.GAME.EARLY_FIVE,
                claimed: false
            },
            {
                score: CONSTANTS.SCORE.TOP_LINE_SCORE,
                price: Number(((totalAmount * TOP_LINE_PERCENTAGE) / (100 * TOP_LINE_PERSON)).toFixed(3)),
                count: TOP_LINE_PERSON,
                type: CONSTANTS.GAME.TOP_LINE,
                claimed: false
            },
            {
                score: CONSTANTS.SCORE.MIDDLE_LINE_SCORE,
                price: Number(((totalAmount * MIDDLE_LINE_PERCENTAGE) / (100 * MIDDLE_LINE_PERSON)).toFixed(3)),
                count: MIDDLE_LINE_PERSON,
                type: CONSTANTS.GAME.MIDDLE_LINE,
                claimed: false
            },
            {
                score: CONSTANTS.SCORE.BOTTOM_LINE_SCORE,
                price: Number(((totalAmount * BOTTOM_LINE_PERCENTAGE) / (100 * BOTTOM_LINE_PERSON)).toFixed(3)),
                count: BOTTOM_LINE_PERSON,
                type: CONSTANTS.GAME.BOTTOM_LINE,
                claimed: false
            },
            {
                score: CONSTANTS.SCORE.CORNERS_SCORE,
                price: Number(((totalAmount * CORNERS_PERCENTAGE) / (100 * CORNERS_PERSON)).toFixed(3)),
                count: CORNERS_PERSON,
                type: CONSTANTS.GAME.CORNERS,
                claimed: false
            },
            {
                score: CONSTANTS.SCORE.FULL_HOUSE_SCORE,
                price: Number(((totalAmount * FULL_HOUSE_PERCENTAGE) / (100 * FULL_HOUSE_PERSON)).toFixed(3)),
                count: FULL_HOUSE_PERSON,
                type: CONSTANTS.GAME.FULL_HOUSE,
                claimed: false
            },
        ]

        const numberArray = await genrateNumbers();

        let tgp = await createTableGamePlay(userId, player.userName, player.userProfile, entryFee, player.ticket, socre, numberArray);
        if (!tgp) { throw new Error("Create Table Game Failed !!"); }

        const tableId = tgp._id;
        socket.handshake.auth.tableId = tableId;
        let count = 0;

        let pgp = await createPlayerGamePlay(userId, tableId);
        if (!pgp) { throw new Error("Create PlayerGamePlay Failed !!"); }

        while (count < player.ticket) {
            const ticket = await create_ticket();
            if (ticket) {

                if (!pgp) { throw new Error("get PlayerGamePlay Failed !!"); }

                count += 1;

                pgp.ticket.push({ ticketNo: count, ticket, selectNumbers: [] })

                pgp = await updatePlayerGamePlay(pgp);
                if (!pgp) { throw new Error("update PlayerGamePlay Failed !!"); }

            }
        }

        player.tableId = tableId;
        player = await updatePlayer(player);
        if (!player) { throw new Error("Get Player Data Failed!"); }

        socket.join(tableId);

        await pushTableInQueue(entryFee, tableId, userId, player.ticket);

        // ! addGameRunningStatus API calls
        const Gamedata: addGameRunningStatusIf = {
            tableId: tgp._id,
            tournamentId: player.lobbyId,
            gameId: player.gameId
        }
        await addGameRunningStatus(Gamedata, player.token, socket.id, player.userId, player.projectType);

        const matchMakingData: BULL_MATCH_MAKING = { userId, tableId }

        await matchMakingTimerQueue(matchMakingData);

        const joinTableResponse: RESPONSE_JOINTABLE = {
            total_Time: MATCH_MAKING_TIMER,
            remaining_Time: MATCH_MAKING_TIMER,
            total_Player: tgp.playerList.length,
            tickets_Cost: totalAmount,
            total_Tickets: tgp.total_ticket,
            full_House: Number(((totalAmount * FULL_HOUSE_PERCENTAGE) / 100).toFixed(3)),
            top_Line: Number(((totalAmount * TOP_LINE_PERCENTAGE) / 100).toFixed(3)),
            middle_line: Number(((totalAmount * MIDDLE_LINE_PERCENTAGE) / 100).toFixed(3)),
            bottom_Line: Number(((totalAmount * BOTTOM_LINE_PERCENTAGE) / 100).toFixed(3)),
            early_Five: Number(((totalAmount * EARLY_FIVE_PERCENTAGE) / 100).toFixed(3)),
            corners: Number(((totalAmount * CORNERS_PERCENTAGE) / 100).toFixed(3)),
        }

        tgp.waitingTable = [joinTableResponse];

        tgp = await updateTableGamePlay(tgp);
        if (!tgp) { throw new Error("Update Table Data Failed!"); }

        Emitter.emit(CONSTANTS.EVENTS.JOIN_TABLE, {
            sentId: tableId,
            data: joinTableResponse,
            message: `${tableId}:${CONSTANTS.RESPONSE.CREATE_TABLE}`,
        });

        return;

    } catch (error: any) {
        console.log('[CreateNewTable] Catch Error :: ', error);
    }

}

export { CreateNewTable };