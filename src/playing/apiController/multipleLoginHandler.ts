
import CryptoJS from "crypto-js";
import Errors from "../errors";
import { getconfig } from "../../config";
import { CONSTANTS, exportObject } from "../../constants";
import logger from "../../logger";
import { getPlayer, updatePlayer } from "../database/player";
import { io } from "../../connection/socket";
import { getTableGamePlay } from "../database/table";
import { leaveTable } from "../../services/leave";
import { Emitter } from "../events/eventEmmiter";

const { PLATFORM } = getconfig();

async function multipleLoginHandler(req: any, res: any) {
    try {
        logger.info('multipleLoginHandler :: req.body  :::', req.body);

        const { EVENTS, MESSAGES, NUMERICAL } = exportObject;

        const authKey = req.headers["authorization"];
        logger.info("multipleLoginHandler :: authKey  :::", authKey);
        let userId = CryptoJS.AES.decrypt(authKey, PLATFORM.SECRET_KEY).toString(CryptoJS.enc.Utf8);
        logger.info("multipleLoginHandler :: userId :::", userId);

        if (!userId) {
            const resObj = {
                status: 400,
                success: true,
                message: "oops ! Something want wrong",
                data: null
            }
            return res.send(resObj);
        }

        const userProfile = await getPlayer(userId);
        if (!userProfile) throw new Error('Unable to get user Profile ');

        // * Socket Get 
        let socket: any = io.sockets.sockets.get(userProfile.socketId);

        if (req.body && req.body.token) {
            userProfile.token = req.body.token;
            await updatePlayer(userProfile);
        }

        if (userProfile && userProfile.tableId !== "") {

            const tableData = await getTableGamePlay(userProfile.tableId);
            if (!tableData) throw new Error('Unable to get table game play');

            if (tableData) {

                await leaveTable({ apiUserId: userId, apiTableId: userProfile.tableId }, socket);

                let nonProdMsg = MESSAGES.ERROR.MULTIPLE_LOGIN_FAILED_MSG;

                Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                    sentId: userProfile.socketId,
                    data: {
                        error: true,
                        message: `${nonProdMsg}`
                    },
                    message: `multipleLoginHandler Function Error !`,
                });

            }

            const resObj = {
                status: 200,
                success: true,
                message: "sucessfully",
                data: null
            }
            return res.send(resObj);
        }
        else {
            throw new Errors.UnknownError('Unable user in table seats');
        }

    } catch (error) {
        console.error('multipleLoginHandler :>> ', error);
        const resObj = {
            status: 400,
            message: "oops ! Something want wrong",
            data: null
        }
        return res.send(resObj);
    }
}

export = multipleLoginHandler;