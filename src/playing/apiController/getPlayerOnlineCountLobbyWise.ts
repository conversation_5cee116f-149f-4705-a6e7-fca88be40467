import { CONSTANTS } from "../../constants";
import logger from "../../logger";
import { REDIS_GETALL } from "../database/redis";

const GetPlayerOnlineCountLobbyWise = async (req: any, res: any) => {

    try {

        logger.info("getPlayerOnlineCountLobbyWise : ", JSON.stringify(req.body));

        const lobbyId = req.body?.lobbyId;

        if (!lobbyId) {

            const ResData = {

                status: 200,
                success: false,
                message: "lobbyId Is Required !",
                data: null
            }

            res.send(ResData);

            return;
        }

        const OnlineUserLobbyWiseKey = `${CONSTANTS.REDIS.ONLINE_PLAYER_LOBBY_WISE}:${lobbyId}:*`;
        const OnlineUserLobbyWise = await REDIS_GETALL(OnlineUserLobbyWiseKey);

        const ResData = {

            status: 200,
            success: true,
            message: "Player Online in lobby ",
            data: OnlineUserLobbyWise.length
        }

        res.send(ResData);


    } catch (error) {
        logger.info('GetPlayerOnlineCountLobbyWise Error', error);
    }
}

export { GetPlayerOnlineCountLobbyWise };