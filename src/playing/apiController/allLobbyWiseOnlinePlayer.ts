
import { CONSTANTS } from "../../constants";
import logger from "../../logger";
import { REDIS_GETALL } from "../database/redis";

const AllLobbyWiseOnlinePlayer = async (req: any, res: any) => {

    try {

        logger.info("AllLobbyWiseOnlinePlayer : ", JSON.stringify(req.body));

        const lobbyIds = req.body?.lobbyIds;

        if (!lobbyIds) {

            const ResData = {

                status: 200,
                success: false,
                message: "lobbyIds Required !",
                data: null
            }

            res.send(ResData);

            return;
        }

        const onlinePlayers: any[] = []


        for (let i = 0; i < lobbyIds.length; i++) {

            const OnlineUserLobbyWiseKey = `${CONSTANTS.REDIS.ONLINE_PLAYER_LOBBY_WISE}:${lobbyIds[i]}:*`;
            const OnlineUserLobbyWise = await REDIS_GETALL(OnlineUserLobbyWiseKey);

            onlinePlayers.push({ lobbyId: lobbyIds[i], lobbyWiseOnlinePlayer: OnlineUserLobbyWise.length });
        }

        const ResData = {

            status: 200,
            success: true,
            message: "online players",
            data: onlinePlayers
        }

        res.send(ResData);

    } catch (error) {
        logger.info('AllLobbyWiseOnlinePlayer Error', error);
    }
}

export { AllLobbyWiseOnlinePlayer };