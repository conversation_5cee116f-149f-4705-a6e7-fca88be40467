import { CONSTANTS } from "../../constants";
import logger from "../../logger";
import { REDIS_GETALL } from "../database/redis";

const GetPlayerOnlineCount = async (req: any, res: any) => {

    try {

        logger.info("GetPlayerOnlineCount : ", JSON.stringify(req.body));

        const OnlineUsersCountKey = `${CONSTANTS.REDIS.ONLINE_PLAYER}:*`
        const OnlineUsersCount = await REDIS_GETALL(OnlineUsersCountKey);

        const ResData = {

            status: 200,
            success: true,
            message: "Online Player",
            data: OnlineUsersCount.length,
        }

        res.send(ResData);

    } catch (error) {
        logger.info('GetPlayerOnlineCount Error', error);
    }
}

export { GetPlayerOnlineCount };