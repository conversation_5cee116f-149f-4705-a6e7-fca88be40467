import { Socket } from "socket.io";
import logger from "../../logger";
import { I_CLAIM_COUNT_RESPONSE } from "../../interface/common";


export const socketResponseExpectOwn = async (
    data: I_CLAIM_COUNT_RESPONSE,
    tableId: string,
    en: string,
    message: string,
    socket: Socket
): Promise<void> => {

    try {

        logger.info('[Response Backend] : ', { en, data });

        socket.to(tableId).emit(en, JSON.stringify({ en, data, message, }));

    } catch (error) {
        console.log('Error in socketResponseExpectOwn : ', error);
    }

}