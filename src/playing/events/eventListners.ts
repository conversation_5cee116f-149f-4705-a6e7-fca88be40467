import { io } from "../../connection/socket";
import { CONSTANTS } from "../../constants";
import { RESPONSE_EMMITER } from "../../interface/event";
import logger from "../../logger";
import { Emitter } from "./eventEmmiter";


const responseEmiter = async (
    data: RESPONSE_EMMITER,
    EVENT_NAME: string,
): Promise<void> => {

    try {

        if (EVENT_NAME !== 'HB') {
            logger.info('[Response Backend] : ', { EVENT_NAME, data });
        }

        io.to(data.sentId).emit(EVENT_NAME, JSON.stringify({
            en: EVENT_NAME,
            data: data.data,
            message: data.message,
        }));

    } catch (error) {
        console.log('Error in responseEmitter : ', error);
    }

}


export const startEventListner = async () => {

    try {

        Emitter.on(CONSTANTS.EVENTS.HB, async (data: RESPONSE_EMMITER) => {
            await responseEmiter(data, CONSTANTS.EVENTS.HB);
        });

        Emitter.on(CONSTANTS.EVENTS.SIGN_UP, async (data: RESPONSE_EMMITER) => {
            await responseEmiter(data, CONSTANTS.EVENTS.SIGN_UP);
        });

        Emitter.on(CONSTANTS.EVENTS.LEAVE_TABLE, async (data: RESPONSE_EMMITER) => {
            await responseEmiter(data, CONSTANTS.EVENTS.LEAVE_TABLE);
        });

        Emitter.on(CONSTANTS.EVENTS.NO_PLAYER, async (data: RESPONSE_EMMITER) => {
            await responseEmiter(data, CONSTANTS.EVENTS.NO_PLAYER);
        });

        Emitter.on(CONSTANTS.EVENTS.ERROR_POPUP, async (data: RESPONSE_EMMITER) => {
            await responseEmiter(data, CONSTANTS.EVENTS.ERROR_POPUP);
        });

        Emitter.on(CONSTANTS.EVENTS.SELECT_TABLE, async (data: RESPONSE_EMMITER) => {
            await responseEmiter(data, CONSTANTS.EVENTS.SELECT_TABLE);
        });

        Emitter.on(CONSTANTS.EVENTS.JOIN_TABLE, async (data: RESPONSE_EMMITER) => {
            await responseEmiter(data, CONSTANTS.EVENTS.JOIN_TABLE);
        });

        Emitter.on(CONSTANTS.EVENTS.GAME_START, async (data: RESPONSE_EMMITER) => {
            await responseEmiter(data, CONSTANTS.EVENTS.GAME_START);
        });

        Emitter.on(CONSTANTS.EVENTS.RE_JOIN, async (data: RESPONSE_EMMITER) => {
            await responseEmiter(data, CONSTANTS.EVENTS.RE_JOIN);
        });

        Emitter.on(CONSTANTS.EVENTS.NUMBER_LIST, async (data: RESPONSE_EMMITER) => {
            await responseEmiter(data, CONSTANTS.EVENTS.NUMBER_LIST);
        });

        Emitter.on(CONSTANTS.EVENTS.RANDOM_BALL, async (data: RESPONSE_EMMITER) => {
            await responseEmiter(data, CONSTANTS.EVENTS.RANDOM_BALL);
        });

        Emitter.on(CONSTANTS.EVENTS.SELECT_NUMBER, async (data: RESPONSE_EMMITER) => {
            await responseEmiter(data, CONSTANTS.EVENTS.SELECT_NUMBER);
        });

        Emitter.on(CONSTANTS.EVENTS.PATTERN, async (data: RESPONSE_EMMITER) => {
            await responseEmiter(data, CONSTANTS.EVENTS.PATTERN);
        });

        Emitter.on(CONSTANTS.EVENTS.WINNING, async (data: RESPONSE_EMMITER) => {
            await responseEmiter(data, CONSTANTS.EVENTS.WINNING);
        });

        Emitter.on(CONSTANTS.EVENTS.PLAYERLIST, async (data: RESPONSE_EMMITER) => {
            await responseEmiter(data, CONSTANTS.EVENTS.PLAYERLIST);
        });

        Emitter.on(CONSTANTS.EVENTS.USERLIST, async (data: RESPONSE_EMMITER) => {
            await responseEmiter(data, CONSTANTS.EVENTS.USERLIST);
        });

        Emitter.on(CONSTANTS.EVENTS.ERROR_TICKET, async (data: RESPONSE_EMMITER) => {
            await responseEmiter(data, CONSTANTS.EVENTS.ERROR_TICKET);
        });

        Emitter.on(CONSTANTS.EVENTS.LAST_GAME, async (data: RESPONSE_EMMITER) => {
            await responseEmiter(data, CONSTANTS.EVENTS.LAST_GAME);
        });

        Emitter.on(CONSTANTS.EVENTS.WINORLOSS, async (data: RESPONSE_EMMITER) => {
            await responseEmiter(data, CONSTANTS.EVENTS.WINORLOSS);
        });


    } catch (error) {
        console.log("[startEventListner] Error >> ", error);
    }

}