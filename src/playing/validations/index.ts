import Joi from "joi";
import { REQUEST_CLAIMED_PATTERN, REQUEST_SELECT_NUMBER, REQUEST_SIGNUP, REQUEST_TICKET_PATTERN } from "../../interface/event";
import { I_MATCH_MAKING, T_TICKET_TYPE } from "../../interface/common";
import { getconfig } from "../../config";

export const signupInputValidator = async (
    data: REQUEST_SIGNUP
): Promise<string | number | boolean | undefined> => {

    const { MIN_TICKET, MAX_TICKET } = getconfig().GAMEPLAY;

    return Joi.object<REQUEST_SIGNUP>({
        userId: Joi.string().required(),
        userName: Joi.string().min(1).max(50).required(),
        userProfile: Joi.string().required(),
        entryFee: Joi.number().allow(0).required(),
        ticket: Joi.number().min(MIN_TICKET).max(MAX_TICKET).required(),
        token: Joi.string().required(),
        lobbyId: Joi.string().required(),
        gameId: Joi.string().required(),
        isFTUE: Joi.boolean().required(),
        projectType: Joi.string().allow("").required(),
        gameModeId: Joi.string().allow("").required(),
    }).options({ allowUnknown: true, stripUnknown: true })
        .validate(data).error?.message;
}


export const selectTableInputValidtor = async (
    data: I_MATCH_MAKING,
): Promise<string | number | undefined> => {

    return Joi.object<I_MATCH_MAKING>({
        entryFee: Joi.number().allow(0).required(),
        ticket: Joi.number().required(),
        userId: Joi.string().required(),
    }).options({ allowUnknown: true, stripUnknown: true })
        .validate(data).error?.message;
}


export const selectNumberInputValidator = async (
    data: REQUEST_SELECT_NUMBER,
    userId: string,
): Promise<string | number | undefined> => {

    const schema = Joi.object<REQUEST_SELECT_NUMBER>({
        tno: Joi.number().integer().min(1).max(20).required(),
        sno: Joi.number().integer().min(1).max(90).required(),
    }).options({ allowUnknown: true, stripUnknown: true });

    const validationError = schema.validate(data).error?.message;

    if (validationError) { return validationError; }

    const userIdSchema = Joi.string().required();
    const userIdError = userIdSchema.validate(userId).error?.message;

    return userIdError ? userIdError : undefined;
}


export const ticketPatternInputValidator = async (
    data: REQUEST_TICKET_PATTERN,
    userId: string,
    tableId: string
): Promise<string | number | undefined> => {

    const schema = Joi.object<REQUEST_TICKET_PATTERN>({
        ticketNumber: Joi.number().integer().min(1).max(20).required(),
        type: Joi.string().required(),
    }).options({ allowUnknown: true, stripUnknown: true });

    const validationError = schema.validate(data).error?.message;
    if (validationError) { return validationError; }

    const userIdSchema = Joi.string().required();
    const userIdError = userIdSchema.validate(userId).error?.message;
    const tableIdSchema = Joi.string().required();
    const tableIdError = tableIdSchema.validate(tableId).error?.message;
    if (userIdError) { return userIdError; }
    if (tableIdError) { return tableIdError; }

}


export const claimedPatternInputValidator = async (
    data: REQUEST_CLAIMED_PATTERN,
    userId: string,
    tableId: string
): Promise<string | number | undefined> => {

    const schema = Joi.object<REQUEST_CLAIMED_PATTERN>({
        ticketNumber: Joi.number().integer().min(1).max(20).required(),
    }).options({ allowUnknown: true, stripUnknown: true });

    const validationError = schema.validate(data).error?.message;
    if (validationError) { return validationError; }

    const userIdSchema = Joi.string().required();
    const userIdError = userIdSchema.validate(userId).error?.message;
    const tableIdSchema = Joi.string().required();
    const tableIdError = tableIdSchema.validate(tableId).error?.message;
    if (userIdError) { return userIdError; }
    if (tableIdError) { return tableIdError; }

}