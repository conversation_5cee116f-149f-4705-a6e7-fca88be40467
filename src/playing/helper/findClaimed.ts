import { I_PATTERN_RESPONSE, I_SCORE } from "../../interface/common";
import logger from "../../logger";
import { getPlayerGamePlay } from "../database/pgp";
import { getTableGamePlay } from "../database/table";


export const findClaimedPattern = async (
    ticketNumber: number,
    tableId: string,
    userId: string,
    isClaim: boolean
): Promise<I_PATTERN_RESPONSE | undefined> => {

    try {

        logger.info("findClaimedPattern ticketNumber : ", ticketNumber);

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        let getPGP = await getPlayerGamePlay(userId,tableId);
        if (!getPGP) { throw new Error("get PlayerGamePlay Data Failed!"); }

        const TableData: I_SCORE[] = [];

        if (getPGP.claimed.length > 0) {

            for (let i = 0; i < getPGP.claimed.length; i++) {

                const findClaimedTicket = getPGP.claimed.filter(n => n.ticketNumber === ticketNumber);

                if (findClaimedTicket.length > 0) {

                    for (let j = 0; j < getTGP.score.length; j++) {

                        if (getTGP.score[j].count > 0) {

                            if (findClaimedTicket.some(n => n.type === getTGP?.score[j].type)) { getTGP.score[j].claimed = true; }
                            else { getTGP.score[j].claimed = false; }

                        } else { getTGP.score[j].claimed = true; }
                        TableData.push(getTGP.score[j]);
                    }
                    break;

                } else {

                    for (let j = 0; j < getTGP.score.length; j++) {
                        if (getTGP.score[j].count === 0) { getTGP.score[j].claimed = true; }
                        else { getTGP.score[j].claimed = false; }
                        TableData.push(getTGP.score[j]);
                    }
                    break;

                }

            }

        } else {

            for (let j = 0; j < getTGP.score.length; j++) {
                if (getTGP.score[j].count === 0) { getTGP.score[j].claimed = true; }
                else { getTGP.score[j].claimed = false; }
                TableData.push(getTGP.score[j]);
            }
        }

        logger.info("[findClaimedPattern] TableData : ", TableData);

        const responseData: I_PATTERN_RESPONSE = {
            pattern: TableData,
            claims: getTGP.claims,
            isClaimTouch: isClaim
        };

        return responseData;

    } catch (error) {
        console.log("findClaimedPattern Catch Error :: ", error);
        return undefined;
    }

}