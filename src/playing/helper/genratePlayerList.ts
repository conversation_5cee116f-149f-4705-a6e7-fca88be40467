import { I_USERLISTS } from "../../interface/common";
import logger from "../../logger";
import { getPlayerGamePlay } from "../database/pgp";
import { getPlayer } from "../database/player";
import { getTableGamePlay, updateTableGamePlay } from "../database/table";


export const genratePlayerList = async (
    tableId: string,
    sizeOfList: number,
    userIds: string,
) => {

    try {

        if (!tableId) { throw new Error("genratePlayerList Validation Error !"); }

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        let list = [];
        let check = false;

        for (let i = 0; i < getTGP.playerList.length; i++) {
            const data = getTGP.playerList[i];

            let getPGP = await getPlayerGamePlay(data.userId, tableId);
            if (getPGP) {

                if (getPGP.tickitScore >= 0 && Number(getPGP.totalclaimedMoney.toFixed(3)) === 0) {
                    logger.info("UserId & TableId :: ", { userIds, tableId });
                    logger.info("tickitScore :: ", getPGP.tickitScore);
                    logger.info("totalclaimedMoney :: ", getPGP.totalclaimedMoney);
                }

                if (data.userId === userIds) {

                    check = true;

                    list.unshift({
                        userId: data.userId,
                        userName: data.userName,
                        userProfile: data.userProfile,
                        score: getPGP.tickitScore,
                        price: Number(getPGP.totalclaimedMoney.toFixed(3))
                    })

                } else {

                    list.push({
                        userId: data.userId,
                        userName: data.userName,
                        userProfile: data.userProfile,
                        score: getPGP.tickitScore,
                        price: Number(getPGP.totalclaimedMoney.toFixed(3))
                    })

                }

                if (list.length === sizeOfList) {

                    if (check) { return list; }
                    else {

                        let getPGPS = await getPlayerGamePlay(userIds, tableId);
                        let getPlayers = await getPlayer(userIds);

                        if (getPGPS && getPlayers) {

                            list.pop();

                            list.unshift({
                                userId: getPlayers.userId,
                                userName: getPlayers.userName,
                                userProfile: getPlayers.userProfile,
                                score: getPGPS.tickitScore,
                                price: Number(getPGP.totalclaimedMoney.toFixed(3))
                            });

                            return list;
                        }
                    }
                }
            }
        }

        return list;

    } catch (error) {
        console.log("GenratePlayerList Error :: ", error);
        return undefined;
    }

}

export const genrateUserList = async (
    tableId: string,
    sizeOfList: number,
    userIds: string,
) => {

    try {

        if (!tableId) { throw new Error("genratePlayerList Validation Error !"); }

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        let getPlayers = await getPlayer(userIds);
        if (!getPlayers) { throw new Error("Get Players Data Failed!"); }

        let getPGP = await getPlayerGamePlay(userIds, tableId);
        if (!getPGP) { throw new Error("Get Pgp Data Failed!"); }

        let list: I_USERLISTS[] = [];

        logger.info("Old userList : ", getTGP.userList);

        const finduserList = getTGP.userList.find((n) => n.userId === userIds);
        logger.info("finduserList : ", finduserList);

        list.unshift({
            userId: userIds,
            userName: getPlayers.userName,
            userProfile: getPlayers.userProfile,
            score: getPGP.tickitScore,
            price: Number(getPGP.totalclaimedMoney.toFixed(3))
        });

        if (finduserList && list.length > 0) {

            list.push(...getTGP.userList.filter(user => user.userId !== userIds));

        } else {

            list.push(...getTGP.userList);

        }

        while (list.length > sizeOfList) {
            list.pop();
        }

        if (list.length <= sizeOfList) {

            getTGP.userList = list;

            getTGP = await updateTableGamePlay(getTGP);
            if (!getTGP) { throw new Error("Update TableGamePlay Not Found !!"); }

        }

        logger.info("New userList : ", getTGP.userList);

        return list;

    } catch (error) {
        console.log("genrateUserList Error :: ", error);
        return undefined;
    }

}

export const genrateShowAllUserList = async (
    tableId: string,
) => {

    try {

        if (!tableId) { throw new Error("genratePlayerList Validation Error !"); }

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        let list = [];

        for (let i = 0; i < getTGP.playerList.length; i++) {
            const userListData = getTGP.playerList[i];
            if (userListData) {

                let getPlayers = await getPlayer(userListData.userId);
                if (!getPlayers) { throw new Error("Get Players Data Failed!"); }

                let getPGP = await getPlayerGamePlay(userListData.userId, tableId);
                if (!getPGP) { throw new Error("Get Pgp Data Failed!"); }

                list.push({
                    userId: userListData.userId,
                    userName: userListData.userName,
                    userProfile: userListData.userProfile,
                    score: getPGP.tickitScore,
                    price: Number(getPGP.totalclaimedMoney.toFixed(3))
                });

            }
        }

        if (list.length === getTGP.playerList.length) {

            list = list.sort((a, b) => b.price - a.price)

        }

        return list;

    } catch (error) {
        console.log("genrateUserList Error :: ", error);
        return undefined;
    }

}
