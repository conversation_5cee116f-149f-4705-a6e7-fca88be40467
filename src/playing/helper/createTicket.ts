import 'dotenv/config';

type Ticket = number[][];
export const create_ticket = async (): Promise<number[][] | undefined> => {

    const ticket: number[] = [];
    let finalTicket: number[][] = [];
    let zero_value: number[][] = [];
    let r_n = 0;

    for (let i = 1; i <= 27; i++) {
        let a = 1, b = 10, c = 19;

        if (i === a || i === b || i === c) {
            r_n = await generateRandomUniqueNumbers([1, 2, 3, 4, 5, 6, 7, 8, 9], ticket);

        } else if (i === (a + 1) || i === (b + 1) || i === (c + 1)) {
            r_n = await generateRandomUniqueNumbers([10, 11, 12, 13, 14, 15, 16, 17, 18, 19], ticket);

        } else if (i === (a + 2) || i === (b + 2) || i === (c + 2)) {
            r_n = await generateRandomUniqueNumbers([20, 21, 22, 23, 24, 25, 26, 27, 28, 29], ticket);

        } else if (i === (a + 3) || i === (b + 3) || i === (c + 3)) {
            r_n = await generateRandomUniqueNumbers([30, 31, 32, 33, 34, 35, 36, 37, 38, 39], ticket);

        } else if (i === (a + 4) || i === (b + 4) || i === (c + 4)) {
            r_n = await generateRandomUniqueNumbers([40, 41, 42, 43, 44, 45, 46, 47, 48, 49], ticket);

        } else if (i === (a + 5) || i === (b + 5) || i === (c + 5)) {
            r_n = await generateRandomUniqueNumbers([50, 51, 52, 53, 54, 55, 56, 57, 58, 59], ticket);

        } else if (i === (a + 6) || i === (b + 6) || i === (c + 6)) {
            r_n = await generateRandomUniqueNumbers([60, 61, 62, 63, 64, 65, 66, 67, 68, 69], ticket);

        } else if (i === (a + 7) || i === (b + 7) || i === (c + 7)) {
            r_n = await generateRandomUniqueNumbers([70, 71, 72, 73, 74, 75, 76, 77, 78, 79], ticket);

        } else if (i === (a + 8) || i === (b + 8) || i === (c + 8)) {
            r_n = await generateRandomUniqueNumbers([80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90], ticket);

        }
        ticket.push(r_n);

    }

    zero_value = await findPattern(zero_value);

    const checkZeroPattern = await manageZero(zero_value);
    if (!checkZeroPattern) {
        zero_value = [];
        zero_value = await findPattern(zero_value);
    }

    for (let m = 0; m < zero_value.length; m++) {
        for (let k = 0; k < zero_value[m].length; k++) {
            const rn = zero_value[m][k] - 1
            ticket[rn] = 0;
        }
    }

    finalTicket = await chunkArray(ticket, 9);

    const hasVerticalLine = await checkVerticalLine(finalTicket);
    if (hasVerticalLine) {

        const hasHorizontalLine = await checkHorizontalLine(finalTicket);
        if (hasHorizontalLine) {

            const checkLines = await checkBothSideLines(finalTicket);
            if (checkLines) {
                return finalTicket;
            }

        }

    }

    return undefined;
}


const generateRandomUniqueNumbers = async (arr: number[], ticket: number[]): Promise<number> => {
    let randomGenrateTicketNumber = arr[Math.floor(Math.random() * arr.length)];

    while (ticket.includes(randomGenrateTicketNumber)) {
        randomGenrateTicketNumber = arr[Math.floor(Math.random() * arr.length)];
    }

    return randomGenrateTicketNumber;
}


const generateRandomUniqueZeroNumbers = async (array: number[], count: number): Promise<number[]> => {

    const uniqueNumbers = new Set<number>();
    while (uniqueNumbers.size < count) {
        const randomIndex = Math.floor(Math.random() * array.length);
        const randomNumber = array[randomIndex];
        uniqueNumbers.add(randomNumber);
    }
    return [...uniqueNumbers];
};


const chunkArray = async (arr: number[], chunkSize: number): Promise<number[][]> => {
    const result: number[][] = [];
    for (let i = 0; i < arr.length; i += chunkSize) {
        result.push(arr.slice(i, i + chunkSize));
    }
    return result;
};


const checkHorizontalLine = async (ticket: Ticket): Promise<boolean> => {
    for (let i = 0; i < 3; i++) {
        const row = ticket[i];
        const zeroCount = row.filter((element) => element === 0).length;
        return zeroCount === 4 ? true : false;
    }
    return false;
};


const checkBothSideLines = async (ticket: Ticket): Promise<boolean> => {

    if ((ticket[0][0] === 0 && ticket[0][1] === 0) || (ticket[0][ticket[0].length - 1] === 0 && ticket[0][ticket[0].length - 2] === 0)) {
        return false;
    } else if ((ticket[1][0] === 0 && ticket[1][1] === 0) || (ticket[1][ticket[1].length - 1] === 0 && ticket[1][ticket[1].length - 2] === 0)) {
        return false;
    } else if ((ticket[2][0] === 0 && ticket[2][1] === 0) || (ticket[2][ticket[2].length - 1] === 0 && ticket[2][ticket[2].length - 2] === 0)) {
        return false;
    }

    return true;

}


const checkVerticalLine = async (ticket: Ticket): Promise<boolean> => {

    for (let i = 0; i < ticket[0].length; i++) {
        if (ticket[0][i] === 0 && ticket[1][i] === 0 && ticket[2][i] === 0) {

            return false;
        }
    }
    return true;
}


const findPattern = async (zero_value: number[][]): Promise<number[][]> => {

    for (let l = 1; l <= 3; l++) {
        let zero_arr: number[] = []
        if (l == 1) { zero_arr = await generateRandomUniqueZeroNumbers([1, 2, 3, 4, 5, 6, 7, 8, 9], 4); }
        else if (l == 2) { zero_arr = await generateRandomUniqueZeroNumbers([10, 11, 12, 13, 14, 15, 16, 17, 18], 4); }
        else if (l == 3) { zero_arr = await generateRandomUniqueZeroNumbers([19, 20, 21, 22, 23, 24, 25, 26, 27], 4); }

        zero_value.push(zero_arr.sort((a, b) => a - b));
    }

    return zero_value;
}


const manageZero = async (value: number[][]): Promise<boolean> => {

    // * Method - 1 
    // for (let i = 0; i < value[0].length; i++) {

    //     const findZeroArray2 = value[1].find(e => { return value[0][i] === (e - 9) })
    //     const findZeroArray3 = value[2].find(e => { return value[0][i] === (e - 18) })

    //     if (findZeroArray2 && findZeroArray3) { return false; }

    // }
    // return true;

    // * Method - 2
    const setArray2 = new Set<number>(value[1].map(e => e - 9));
    const setArray3 = new Set<number>(value[2].map(e => e - 18));

    for (let i = 0; i < value[0].length; i++) {

        if (setArray2.has(value[0][i]) && setArray3.has(value[0][i])) {
            return false;
        }
    }

    return true;

}
