const COLOR: Record<string, string> = {
    '1': 'blue',
    '2': 'purple',
    '3': 'yellow',
    '4': 'red',
    '0': 'green'
}

export const convertStringtoObject = (obj: object): object => {
    try {
        return typeof obj === 'object' ? obj : JSON.parse(obj);
    } catch (error: any) {
        return error;
    }
}

export const genrateNumbers = async (): Promise<number[]> => {

    // * Method -1
    // let numbers: number[] = [];
    // for (let i = 1; i <= 90; i++) { numbers.push(i); }
    // return numbers;

    // * Method -2
    return Array.from({ length: 90 }, (_, index) => index + 1);

}

export const getColor = async (numbers: number[]): Promise<string> => {
    return COLOR[`${((numbers.length + 1) % 5)}`];
}
