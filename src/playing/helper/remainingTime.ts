import logger from "../../logger";


export const remainingTimeCalculation = async (Job: any): Promise<number> => {

    try {

        logger.info("RemainTimeCalculation : ", Job);

        if (!Job) { return 0; };

        const RemainingTime: number = (Date.now() - Job.timestamp) / 1000;

        const FixedRemainingTime: number = Number(RemainingTime.toFixed(3));

        const JobDelayTimer: number = Job?.opts?.delay ? Job.opts.delay : 1;

        const JobDelayTimerInSecond: number = JobDelayTimer / 1000;

        const FixedJobDelayTimer: number = Number(JobDelayTimerInSecond.toFixed(3));

        const FinalRemainingTime: number = FixedJobDelayTimer - (FixedRemainingTime * 1);

        const FixedFinalRemainingTime: number = Number(FinalRemainingTime.toFixed(3));

        if (FixedFinalRemainingTime < 0) { return 0; };

        logger.info("RemainTimeCalculation Return : ", FixedFinalRemainingTime);

        return FixedFinalRemainingTime;

    } catch (error: any) {
        console.log('RemainTimeCalculation Error : ', { error });
        return 0;
    };
};


