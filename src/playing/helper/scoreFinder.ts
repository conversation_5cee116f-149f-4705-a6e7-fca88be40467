import { getconfig } from "../../config";
import { CONSTANTS } from "../../constants";
import { I_ALL_WINNER, I_SCORELIST } from "../../interface/common";
import logger from "../../logger";
import { getPlayerGamePlay } from "../database/pgp";
import { getTableGamePlay } from "../database/table";

export const findScore = async (
    userId: string,
    tableId: string
): Promise<number> => {

    try {

        if (!userId || !tableId) { throw new Error("[findScore] Validation Error !"); }
        let score = 0;

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        const filteredPlayers = getTGP.scoreList.filter(player => player.userId === userId);
        if (filteredPlayers.length > 0) {
            score = filteredPlayers.reduce((total, player) => total + player.score, 0);
            return score;
        } else {
            logger.info(`No players found with userId ${userId}`,"");
            return score;
        }

    } catch (error) {
        logger.info("findScore Catch Error :: ", error);
        return 0;
    }

}

export const findMoney = async (
    userId: string,
    tableId: string
): Promise<number> => {

    try {

        if (!userId || !tableId) { throw new Error("[findMoney] Validation Error !"); }

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        const filteredScoreList = getTGP.scoreList.filter((item: any) => item.userId === userId);
        logger.info("Filtered Score List:", filteredScoreList);

        const price = filteredScoreList.reduce((acc: any, curr: any) => acc + curr.price, 0);
        logger.info("Total Cliamed Price:", Number(price.toFixed(3)));

        return Number(price.toFixed(3));

    } catch (error) {
        logger.info("findMoney Catch Error :: ", error);
        return 0;
    }

}


export const winnerResponsefinder = async (
    tableId: string,
    isApi: boolean,
): Promise<I_ALL_WINNER[] | undefined> => {

    try {

        if (!tableId) { throw new Error("[winnerResponsefinder] Validation Error !"); }

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        let filteredPlayers: any[] = [];

        if (isApi) { filteredPlayers = getTGP.playerList.filter(player => !player.leave); }
        else { filteredPlayers = getTGP.playerList; }

        let Final: I_ALL_WINNER[] = filteredPlayers.map(player => {

            if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }
            const userClaimRecord: I_SCORELIST[] = getTGP.scoreList.filter((item: I_SCORELIST) => item.userId === player.userId);

            const score = userClaimRecord.reduce((acc, curr) => acc + curr.score, 0);
            const price = userClaimRecord.reduce((acc, curr) => acc + curr.price, 0);

            return {
                userId: player.userId,
                userName: player.userName,
                userProfile: player.userProfile,
                score,
                won: Number(price.toFixed(3)),
                type: 'EARLY_FIVE',
                you: false,
            };
        });

        Final = Final.filter(item => item !== undefined) as I_ALL_WINNER[];
        Final = Final.sort((a: I_ALL_WINNER, b: I_ALL_WINNER) => b.won - a.won);

        return Final;

    } catch (error) {
        logger.info("winnerResponsefinder Catch Error :: ", error);
        return undefined;
    }

}


export const winOrLostScore = async (
    userId: string,
    tableId: string,
): Promise<string | undefined> => {

    try {

        if (!userId || !tableId) { throw new Error("[winOrLostScore] Validation Error !"); }

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        let getPGP = await getPlayerGamePlay(userId, tableId);
        if (!getPGP) { throw new Error("Get Player Game Play Data Failed!"); }

        let count = 0, message = "", fullhouse = 0;

        for (let i = 0; i < getTGP.score.length; i++) {

            if (getTGP.score[i].type === CONSTANTS.GAME.FULL_HOUSE) {
                fullhouse = getTGP.score[i].score;
            } else {
                count += getTGP.score[i].score;
            }

        }

        logger.info("One Ticket total Score : ", count);

        const totalTicketScores = Number(getPGP.ticket.length * (count + fullhouse));
        logger.info("Total Ticket Score : ", totalTicketScores);

        const totalWinScore = await findScore(userId, tableId);
        logger.info("totalWinScore : ", totalWinScore);

        if (totalWinScore < totalTicketScores) {
            const result = totalTicketScores - totalWinScore;
            message = `YOU LOST ${Number(result.toFixed(3))} CHIPS`;
        } else {

            message = `YOU WIN ${Number(totalWinScore.toFixed(3))} CHIPS`;

        }

        return message;

    } catch (error) {
        logger.info("winOrLostScore Catch Error :: ", error);
        return undefined;
    }

}


export const winOrLostMoney = async (
    userId: string,
    tableId: string
): Promise<string | undefined> => {

    try {
        const { PLATFORM_RAKE } = getconfig().GAMEPLAY;

        if (!userId || !tableId) { throw new Error("[winOrLostMoney] Validation Error !"); }

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        let getPGP = await getPlayerGamePlay(userId, tableId);
        if (!getPGP) { throw new Error("Get Player Game Play Data Failed!"); }

        let count = 0, message = "", fullhouse = 0;

        for (let i = 0; i < getTGP.score.length; i++) {

            if (getTGP.score[i].type === CONSTANTS.GAME.FULL_HOUSE) {
                fullhouse = Number(getTGP.score[i].price.toFixed(3));
            } else {
                count += Number(getTGP.score[i].price.toFixed(3));
            }

        }

        logger.info("One Ticket total Money : ", count);

        const totalTicketMoney = Number(getTGP.waitingTable[0].tickets_Cost.toFixed(3));
        logger.info("Total Ticket Money : ", totalTicketMoney);

        // const totalOwnTicketMoney = Number((getTGP.entryFee * getPGP.ticket.length).toFixed(3));
        const totalOwnTicketMoney = Number((getTGP.entryFee - (getTGP.entryFee * (PLATFORM_RAKE / 100))).toFixed(3));
        logger.info("Total Own Ticket Money : ", totalOwnTicketMoney);

        const totalWinMoney = await findMoney(userId, tableId);
        logger.info("Total Win Money : ", totalWinMoney);

        if (totalWinMoney < totalOwnTicketMoney) {
            const result = totalOwnTicketMoney - totalWinMoney;
            message = `YOU LOST ${Number(result.toFixed(3))} CHIPS`;
        } else {
            message = `YOU WIN ${Number(totalWinMoney.toFixed(3))} CHIPS`;
        }

        return message;

    } catch (error) {
        logger.info("winOrLostMoney Catch Error :: ", error);
        return undefined;
    }

}