import { getTableGamePlay } from "../database/table";


export const randomNumberShuffle = async (
    tableId: string
): Promise<number[] | undefined> => {

    try {

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("get TableGamePlay Not Found !"); }

        const shuffle = await shuffleArray(getTGP.totalNumbers);
        if (shuffle) { return shuffle; }

    } catch (error) {
        console.log("[randomNumberShuffle] Error: ", error);
        return undefined;
    }

}


export const shuffleArray = async (array: number[]): Promise<number[]> => {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }

    return array;
}