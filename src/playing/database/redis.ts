import { redisClient } from "../../connection/redis";
import { CONSTANTS } from "../../constants";
import logger from "../../logger";


export const pushTableInQueue = async (
    entryFee: number,
    tableId: string,
    userId: string,
    ticket: number,
) => {
    try {

        const key = `${CONSTANTS.REDIS.MATCHMAKING}:${entryFee}:${ticket}`;
        const pushData = await REDIS_RPUSH(key, tableId);
        return pushData;

    } catch (error) {
        console.log("[pushTableInQueue] Error >> ", error);
        return error;
    }

}


export const getMatchMaking = async (
    entryFee: number,
    minPlayer: number,
    ticket: number,
) => {
    try {

        const key = `${CONSTANTS.REDIS.MATCHMAKING}:${entryFee}:${ticket}`;
        const getTableGamePlay = await REDIS_LRANGE(key, minPlayer);
        if (getTableGamePlay) { return getTableGamePlay; }

    } catch (error: any) {
        console.log("[getMatchMaking] Error >> ", error);
        return error;
    }
}


export const removeValueFromArray = async (entryFee: number, tableId: string, ticket: number) => {

    try {

        const key = `${CONSTANTS.REDIS.MATCHMAKING}:${entryFee}:${ticket}`;

        const removeTableData = await REDIS_LREM(key, tableId);
        logger.info('temp - removeValueFromArray >>>>', removeTableData);

    } catch (error) {
        console.log("[removeValueFromArray] >> Error  >> ", error);
    }

}


export const REDIS_LREM = async (
    key: string,
    tableId: string
) => {

    try {
        const value = JSON.stringify(tableId);

        return await redisClient.LREM(key, 1, value);

    } catch (error) {
        console.log("[REDIS_LREM] >> Error  >> ", error);
    }

}


export const REDIS_LRANGE = async (
    key: string,
    minPlayer: number,
): Promise<Array<string> | undefined> => {

    try {
        const Data = await redisClient.LRANGE(key, 0, minPlayer);  // LRANGE(key, start, stop)
        if (Data) return Data;

        return undefined;

    } catch (error: any) {
        console.log("[REDIS_LRANGE] Error >> ", error);
        return error;
    }
}


export const REDIS_RPUSH = async (
    key: string,
    tableId: string,
) => {

    try {
        return await redisClient.RPUSH(key, JSON.stringify(tableId));

    } catch (error) {
        console.log("[REDIS_LPUSH] Error >> ", error);
        return error;
    }

}


export const REDIS_SET = async (
    key: string,
    value: object
) => {
    try {

        const Data = await redisClient.set(key, JSON.stringify(value));
        // const Data = await redisClient.setEx(key, CONSTANTS.REDIS.DELETE_REDIS_DATA, JSON.stringify(value));
        return Data;

    } catch (error) {
        console.log("[REDIS_SET] Error >> ", error);
        return error;
    }
}


export const REDIS_GET = async (
    key: string,
) => {
    try {

        let Data = await redisClient.get(key);
        if (Data) return Data = JSON.parse(Data);

        return '';

    } catch (error) {
        console.log("[REDIS_GET] Error >> ", error);
        return error;
    }
}


export const REDIS_DELETE = async (
    key: string,
) => {
    try {
        return await redisClient.del(key);

    } catch (error) {
        console.log("[REDIS_DELETE] Error >> ", error);
        return error;
    }
}


export const REDIS_GETALL = async (
    key: string,
) => {
    try {
        const Data = await redisClient.KEYS(key);
        if (Data) return Data;

    } catch (error: any) {
        console.log("[REDIS_GETALL] Error >> ", error);
        return error;
    }

}