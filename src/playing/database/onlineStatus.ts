import { CONSTANTS } from "../../constants";
import logger from "../../logger";
import { REDIS_DELETE, REDIS_GET, REDIS_GETALL, REDIS_SET } from "./redis";


export const setOnlineUser = async (userId: string, data: any) => {
    try {

        const key = `${CONSTANTS.REDIS.ONLINE_PLAYER}:${userId}`;
        return await REDIS_SET(key, data);

    } catch (error) {
        console.log(`CATCH_ERROR :: [setOnlineUser] >>>`, error);
        throw error;
    }
}

export const setOnlineUserLobbyWise = async (userId: string, lobbyId: string, data: any) => {
    try {
        const key = `${CONSTANTS.REDIS.ONLINE_PLAYER_LOBBY_WISE}:${lobbyId}:${userId}`;
        return await REDIS_SET(key, data);

    } catch (error) {
        console.log(`CATCH_ERROR :: [setOnlineUser] >>>`, error);
        throw error;
    }
}

export const getOnlineUser = async (userId: string) => {
    try {
        const key = `${CONSTANTS.REDIS.ONLINE_PLAYER}:${userId}`;
        return await REDIS_GET(key);

    } catch (error) {
        console.log(`CATCH_ERROR :: [getOnlineUser] >>> `, error);
        throw error;
    }
}

export const getOnlineUserLobbyWise = async (userId: string, lobbyId: string) => {
    try {
        const key = `${CONSTANTS.REDIS.ONLINE_PLAYER_LOBBY_WISE}:${lobbyId}:${userId}`;
        return await REDIS_GET(key);

    } catch (error) {
        console.log(`CATCH_ERROR :: [getOnlineUserLobbyWise] >>>`, error);
        throw error;
    }
}

export const removeOnlineUser = async (userId: string) => {
    try {
        const key = `${CONSTANTS.REDIS.ONLINE_PLAYER}:${userId}`;
        return await REDIS_DELETE(key);

    } catch (error) {
        console.log(`CATCH_ERROR in :: [removeOnlineUser] >>> `, error);
        throw error;
    }
}

export const removeOnlineUserLobbyWise = async (userId: string, lobbyId: string) => {
    try {
        const key = `${CONSTANTS.REDIS.ONLINE_PLAYER_LOBBY_WISE}:${lobbyId}:${userId}`;
        return await REDIS_DELETE(key);

    } catch (error) {
        console.log(`CATCH_ERROR :: [removeOnlineUserLobbyWise] >>>`, error);
        throw error;
    }
}

export const getAllOnlineUser = async () => {
    try {
        const key = `${CONSTANTS.REDIS.ONLINE_PLAYER}:*`;
        return await REDIS_GETALL(key);

    } catch (error) {
        console.log(`CATCH_ERROR :: [getAllOnlineUser] >>>`, error);
        throw error;
    }
}

export const getAllOnlineUserLobbyWise = async (lobbyId: string) => {
    try {
        const key = `${CONSTANTS.REDIS.ONLINE_PLAYER_LOBBY_WISE}:${lobbyId}:*`;
        return await REDIS_GETALL(key);

    } catch (error) {
        console.log(`CATCH_ERROR :: [getAllOnlineUserLobbyWise] >>>`, error);
        throw error;
    }
}