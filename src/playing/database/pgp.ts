import { CONSTANTS } from "../../constants";
import { I_CLAIMED, I_INVALID_TICKET, I_TICKETS, T_PLAYER_STATE } from "../../interface/common";
import logger from "../../logger";
import { REDIS_GET, REDIS_SET } from "./redis";


export class PlayerGamePlay {
    userId: string;
    tableId: string;
    tickitScore: number;
    totalclaimedMoney: number;
    playerState: T_PLAYER_STATE;
    ticket: Array<I_TICKETS>;
    claimed: Array<I_CLAIMED>;
    isLost: boolean;
    lostList: Array<I_INVALID_TICKET>;
    isAvaliable: boolean;
    isDisconnect: boolean;
    isLeave: boolean;

    constructor(
        userId: string,
        tableId: string,
    ) {
        this.userId = userId;
        this.tableId = tableId;
        this.tickitScore = 0;
        this.totalclaimedMoney = 0;
        this.playerState = CONSTANTS.STATE.INITIAL;
        this.ticket = [];
        this.claimed = [];
        this.isLost = false;
        this.lostList = [];
        this.isAvaliable = false;
        this.isDisconnect = false;
        this.isLeave = false;
    }
}

export const createPlayerGamePlay = async (
    userId: string,
    tableId: string
): Promise<PlayerGamePlay | undefined> => {

    try {

        const pgp = new PlayerGamePlay(userId, tableId);

        const key = `${CONSTANTS.REDIS.PLAYER_GAME_PLAY}:${userId}:${tableId}`;
        await REDIS_SET(key, pgp);
        return pgp;

    } catch (error: any) {
        console.log('[createPlayerGamePlay] Error >> ', error);
        return error;
    }

}

export const getPlayerGamePlay = async (
    userId: string,
    tableId: string,
): Promise<PlayerGamePlay | undefined> => {

    try {
        const key = `${CONSTANTS.REDIS.PLAYER_GAME_PLAY}:${userId}:${tableId}`;

        const data = await REDIS_GET(key);
        if (data) return data;

    } catch (error: any) {
        console.log("[getPlayerGamePlay] Error >> ", error);
        return error;
    }

}


export const updatePlayerGamePlay = async (
    pgp: PlayerGamePlay
): Promise<PlayerGamePlay | undefined> => {
    try {

        const key = `${CONSTANTS.REDIS.PLAYER_GAME_PLAY}:${pgp.userId}:${pgp.tableId}`;
        const Data = await REDIS_SET(key, pgp);
        if (Data) return pgp;

    } catch (error: any) {
        console.log("[updatePlayerGamePlay] Error >> ", error);
        return error;
    }
}