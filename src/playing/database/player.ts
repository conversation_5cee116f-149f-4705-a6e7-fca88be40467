import { CONSTANTS } from "../../constants";
import { REQUEST_SIGNUP } from "../../interface/event";
import logger from "../../logger";
import { REDIS_GET, REDIS_SET } from "./redis";


export class Player {
    userId: string;
    userName: string;
    userProfile: string;
    socketId: string;
    tableId: string;
    token: string;
    gameId: string;
    lobbyId: string;
    latitude: string;
    longitude: string;
    balance: number;
    isFTUE: boolean;
    entryFee: number;
    ticket: number;
    projectType: string;
    gameModeId: string;

    constructor(
        userId: string,
        userName: string,
        userProfile: string,
        socketId: string,
        token: string,
        gameId: string,
        lobbyId: string,
        latitude: string,
        longitude: string,
        balance: number,
        isFTUE: boolean,
        entryFee: number,
        ticket: number,
        projectType: string,
        gameModeId: string
    ) {
        this.userId = userId;
        this.userName = userName;
        this.userProfile = userProfile;
        this.socketId = socketId;
        this.tableId = '';
        this.token = token;
        this.gameId = gameId;
        this.lobbyId = lobbyId;
        this.userId = userId;
        this.latitude = latitude;
        this.longitude = longitude;
        this.balance = balance;
        this.isFTUE = isFTUE;
        this.entryFee = entryFee;
        this.ticket = ticket;
        this.projectType = projectType;
        this.gameModeId = gameModeId;
    }
}


export const findUser = async (
    userId: string,
): Promise<Player | undefined> => {
    try {

        const key = `${CONSTANTS.REDIS.PLAYER}:${userId}`;

        const Data = await REDIS_GET(key);
        if (Data) return Data;

    } catch (error) {
        console.log("[findUser] Error >> ", error);
        return undefined;
    }
}



export const createNewPlayer = async (
    data: REQUEST_SIGNUP,
    clientId: string
): Promise<Player | undefined> => {
    try {

        const { userId, userName, userProfile, token, gameId, lobbyId, isFTUE, entryFee, ticket, projectType, gameModeId } = data;

        let latitude = "";
        let longitude = "";
        let balance = 0;

        const user = new Player(
            userId,
            userName,
            userProfile,
            clientId,
            token,
            gameId,
            lobbyId,
            latitude,
            longitude,
            balance,
            isFTUE,
            entryFee,
            Number(ticket),
            projectType,
            gameModeId
        );

        const key = `${CONSTANTS.REDIS.PLAYER}:${userId}`;

        const Data = await REDIS_SET(key, user);
        return Data ? user : undefined

    } catch (error) {
        console.log("[createNewPlayer] Error >> ", error);
        return undefined;
    }
}


export const getPlayer = async (
    userId: string,
): Promise<Player | undefined> => {

    try {

        const key = `${CONSTANTS.REDIS.PLAYER}:${userId}`;

        const Data = await REDIS_GET(key);
        if (Data) return Data;

    } catch (error) {
        console.log("[getPlayer] Error >> ", error);
        return undefined;
    }

}


export const updatePlayer = async (
    data: Player,
): Promise<Player | undefined> => {
    try {

        const key = `${CONSTANTS.REDIS.PLAYER}:${data.userId}`;

        const Data = await REDIS_SET(key, data);
        if (Data) return data;

    } catch (error) {
        console.log("[updatePlayer] Error >> ", error);
        return undefined;
    }
}