import { CONSTANTS } from "../../constants";
import { I_COLOR_BALL_TIMER, I_PLAYERLIST, I_SCORE, I_SCORELIST, I_USERLISTS, T_TABLE_STATE } from "../../interface/common";
import { RESPONSE_JOINTABLE } from "../../interface/event";
import { REDIS_GET, REDIS_SET } from "./redis";



export class TableGamePlay {
    _id: string;
    entryFee: number;
    playerList: Array<I_PLAYERLIST>;
    lock: boolean;
    total_ticket: number;
    claims: number;
    waitingTable: RESPONSE_JOINTABLE[];
    score: I_SCORE[];
    scoreList: I_SCORELIST[];
    totalNumbers: number[];
    randomNumbers: number[];
    tableState: T_TABLE_STATE;
    colorBall: I_COLOR_BALL_TIMER[];
    userList: I_USERLISTS[];

    constructor(
        userId: string,
        userName: string,
        userProfile: string,
        entryFee: number,
        tickets: number,
        score: I_SCORE[],
        totalNumbers: number[],
    ) {
        this._id = String(new Date().getTime());
        this.entryFee = entryFee;
        this.playerList = [
            {
                userId,
                userName,
                userProfile,
                leave: false
            },
        ];
        this.lock = false;
        this.total_ticket = tickets;
        this.claims = 0;
        this.waitingTable = [];
        this.score = score;
        this.scoreList = []
        this.totalNumbers = totalNumbers;
        this.randomNumbers = [];
        this.tableState = CONSTANTS.STATE.WAITING_FOR_PLAYER;
        this.colorBall = [] as I_COLOR_BALL_TIMER[];
        this.userList = [] as I_USERLISTS[];
    }
}



export const createTableGamePlay = async (
    userId: string,
    userName: string,
    userProfile: string,
    entryFee: number,
    tickets: number,
    score: I_SCORE[],
    totalNumbers: number[]
): Promise<TableGamePlay | undefined> => {

    try {

        const Tgp = new TableGamePlay(
            userId,
            userName,
            userProfile,
            entryFee,
            tickets,
            score,
            totalNumbers,
        );

        const key = `${CONSTANTS.REDIS.TABLE_GAME_PLAY}:${Tgp._id}`;
        await REDIS_SET(key, Tgp);
        return Tgp;

    } catch (error: any) {
        console.error('[createTableGamePlay] Error >> ', error);
        return error;
    }

}

export const getTableGamePlay = async (
    tableId: string,
): Promise<TableGamePlay | undefined> => {
    try {

        const key = `${CONSTANTS.REDIS.TABLE_GAME_PLAY}:${tableId}`;

        const GetData = await REDIS_GET(key);
        if (GetData) return GetData;

    } catch (error: any) {
        console.log('[getTableGamePlay] Error >> ', error);
        return error;
    }

}


export const updateTableGamePlay = async (
    data: TableGamePlay,
): Promise<TableGamePlay | undefined> => {

    try {
        const key = `${CONSTANTS.REDIS.TABLE_GAME_PLAY}:${data._id}`;
        const Data = await REDIS_SET(key, data);
        if (Data) return data;

    } catch (error: any) {
        console.log('[updateTableGamePlay] Error >> ', error);
        return error;
    }

}