import { Socket } from "socket.io";
import logger from "../../logger";
import { CONSTANTS } from "../../constants";
import { Lock } from "../../connection/redlock";
import { getPlayer, updatePlayer } from "../database/player";
import { io } from "../../connection/socket";
import { getOnlineUser, removeOnlineUser, removeOnlineUserLobbyWise } from "../database/onlineStatus";
import { getTableGamePlay, updateTableGamePlay } from "../database/table";
import { getPlayerGamePlay, updatePlayerGamePlay } from "../database/pgp";
import { disconnectTimerQueue } from "../../bull/queue/disconnectTimerQueue";
import { removeData, removeDataPlayer } from "../controller/removeData/removeDbData";
import { REDIS_DELETE, removeValueFromArray } from "../database/redis";
import { Emitter } from "../events/eventEmmiter";
import Bull from "bull";
import { matchMakingTimer } from "../../bull/allJobs";
import { remainingTimeCalculation } from "../helper/remainingTime";
import { RESPONSE_JOINTABLE } from "../../interface/event";
import { getconfig } from "../../config";
import { markCompletedGameStatus } from "../clientSideApi";


export const disconnectHandler = async (
    client: Socket
): Promise<void> => {

    const userId = client.handshake.auth?.id;
    const tableId = client.handshake.auth.tableId;
    logger.info("disconnectHandler userId & tableId : ", { userId, tableId });

    try {

        if (!userId || !tableId) {
            console.log("disconnectHandler userId & tableId not Found.");
            return;
        }
        const {
            FULL_HOUSE_PERCENTAGE,
            TOP_LINE_PERCENTAGE,
            MIDDLE_LINE_PERCENTAGE,
            BOTTOM_LINE_PERCENTAGE,
            EARLY_FIVE_PERCENTAGE,
            CORNERS_PERCENTAGE,
            FULL_HOUSE_PERSON,
            TOP_LINE_PERSON,
            MIDDLE_LINE_PERSON,
            BOTTOM_LINE_PERSON,
            EARLY_FIVE_PERSON,
            CORNERS_PERSON,
            MATCH_MAKING_TIMER,
            PLATFORM_RAKE
        } = getconfig().GAMEPLAY;

        const key = `${CONSTANTS.REDLOCK.LOCK}:${CONSTANTS.REDIS.PLAYER}:${userId}`;
        const lock = await Lock.getLock().acquire([key], 2000);

        const key2 = `${CONSTANTS.REDLOCK.LOCK}:${CONSTANTS.REDIS.TABLE_GAME_PLAY}:${tableId}`;
        const locks = await Lock.getLock().acquire([key2], 2000);

        let player = await getPlayer(userId);
        if (player) {

            logger.info("[disconnectHandler] - getplayer ", "");

            const socket = io.sockets.sockets.get(player.socketId);
            if (socket) {
                logger.info(">> If Socket Is Connected Then Return", "");
                await Lock.getLock().release(locks);
                await Lock.getLock().release(lock);
                return;
            }

            let getPGP = await getPlayerGamePlay(userId, tableId);
            if (getPGP) {
                logger.info("[disconnectHandler] - getPGP", "");

                getPGP.playerState = CONSTANTS.STATE.DISCONNECT;
                getPGP = await updatePlayerGamePlay(getPGP);
            }

            let getTGP = await getTableGamePlay(tableId);
            if (getTGP) {

                logger.info("[disconnectHandler] - getTGP state : ", getTGP.tableState);

                if (getTGP.tableState === CONSTANTS.STATE.WAITING_FOR_PLAYER && !getTGP.lock) {

                    logger.info("[disconnectHandler] - playerlist Length : ", getTGP.playerList.length);

                    if (getTGP.playerList.length <= 1) {

                        logger.info("Only One Player in Table , So Game End", { PlayerList: getTGP.playerList });

                        // ! Game Is AllMost Complete API Call
                        await markCompletedGameStatus(
                            {
                                tableId: tableId,
                                gameId: player.gameId,
                                tournamentId: player.lobbyId,
                            },
                            player.token,
                            player.socketId,
                            player.projectType
                        );

                        await removeValueFromArray(getTGP.entryFee, tableId, player.ticket);

                        await removeData(userId, tableId);

                    } else {

                        logger.info("Multiple Player in Table , So Leave Game.", { PlayerList: getTGP.playerList });

                        if (!getPGP) { throw new Error("get PlayerGamePlay Failed !!"); }

                        const updetedPlayerList = getTGP.playerList.filter(n => n.userId !== userId);
                        getTGP.playerList = updetedPlayerList;
                        getTGP.total_ticket = getTGP.total_ticket - getPGP.ticket.length;

                        getTGP = await updateTableGamePlay(getTGP);
                        if (!getTGP) { throw new Error("Update TableGamePlay Not Found !!"); }

                        // ! Game Is AllMost Complete API Call
                        await markCompletedGameStatus(
                            {
                                tableId: tableId,
                                gameId: player.gameId,
                                tournamentId: player.lobbyId,
                            },
                            player.token,
                            player.socketId,
                            player.projectType
                        );

                        const key = `${CONSTANTS.REDIS.PLAYER_GAME_PLAY}:${userId}:${tableId}`;
                        await REDIS_DELETE(key);

                        // const totalAmount = getTGP.total_ticket * player.entryFee; // & for multiple Tickets
                        const totalAmount = getTGP.playerList.length * (player.entryFee - (player.entryFee * (PLATFORM_RAKE / 100)));

                        const jobKey: Bull.JobId = `${CONSTANTS.BULL.MATCHMAKING_BULL}:${tableId}`;
                        const job = await matchMakingTimer.getJob(jobKey);

                        const remainingMatchMakiingTime = await remainingTimeCalculation(job);

                        const joinTableResponse: RESPONSE_JOINTABLE = {
                            total_Time: MATCH_MAKING_TIMER,
                            remaining_Time: remainingMatchMakiingTime,
                            total_Player: getTGP.playerList.length,
                            tickets_Cost: totalAmount,
                            total_Tickets: getTGP.total_ticket,
                            full_House: Number(((totalAmount * FULL_HOUSE_PERCENTAGE) / 100).toFixed(3)),
                            top_Line: Number(((totalAmount * TOP_LINE_PERCENTAGE) / 100).toFixed(3)),
                            middle_line: Number(((totalAmount * MIDDLE_LINE_PERCENTAGE) / 100).toFixed(3)),
                            bottom_Line: Number(((totalAmount * BOTTOM_LINE_PERCENTAGE) / 100).toFixed(3)),
                            early_Five: Number(((totalAmount * EARLY_FIVE_PERCENTAGE) / 100).toFixed(3)),
                            corners: Number(((totalAmount * CORNERS_PERCENTAGE) / 100).toFixed(3)),
                        }

                        getTGP.waitingTable = [joinTableResponse];

                        for (let i = 0; i < getTGP.score.length; i++) {
                            if (getTGP.score[i].type === CONSTANTS.EVENTS.EARLY_FIVE) { getTGP.score[i].price = Number(((totalAmount * EARLY_FIVE_PERCENTAGE) / (100 * EARLY_FIVE_PERSON)).toFixed(3)) }
                            else if (getTGP.score[i].type === CONSTANTS.EVENTS.CORNERS) { getTGP.score[i].price = Number(((totalAmount * CORNERS_PERCENTAGE) / (100 * CORNERS_PERSON)).toFixed(3)) }
                            else if (getTGP.score[i].type === CONSTANTS.EVENTS.TOP_LINE) { getTGP.score[i].price = Number(((totalAmount * TOP_LINE_PERCENTAGE) / (100 * TOP_LINE_PERSON)).toFixed(3)) }
                            else if (getTGP.score[i].type === CONSTANTS.EVENTS.MIDDLE_LINE) { getTGP.score[i].price = Number(((totalAmount * MIDDLE_LINE_PERCENTAGE) / (100 * MIDDLE_LINE_PERSON)).toFixed(3)) }
                            else if (getTGP.score[i].type === CONSTANTS.EVENTS.BOTTOM_LINE) { getTGP.score[i].price = Number(((totalAmount * BOTTOM_LINE_PERCENTAGE) / (100 * BOTTOM_LINE_PERSON)).toFixed(3)) }
                            else if (getTGP.score[i].type === CONSTANTS.EVENTS.FULL_HOUSE) { getTGP.score[i].price = Number(((totalAmount * FULL_HOUSE_PERCENTAGE) / (100 * FULL_HOUSE_PERSON)).toFixed(3)) }
                        }

                        getTGP = await updateTableGamePlay(getTGP);
                        if (!getTGP) { throw new Error("Update Table Data Failed!"); }

                        player.tableId = "";

                        await updatePlayer(player);

                        Emitter.emit(CONSTANTS.EVENTS.JOIN_TABLE, {
                            sentId: tableId,
                            data: joinTableResponse,
                            message: `${tableId}:${CONSTANTS.RESPONSE.UPDATE_JOIN_TABLE}`,
                        });

                        await removeDataPlayer(userId, tableId);

                    }

                    await Lock.getLock().release(locks);

                    await Lock.getLock().release(lock);

                    client.handshake.auth = {};

                    client.leave(tableId);

                    return;

                } else if (getTGP.tableState === CONSTANTS.STATE.WIN) {

                    // ! Game Is AllMost Complete API Call
                    await markCompletedGameStatus(
                        {
                            tableId: tableId,
                            gameId: player.gameId,
                            tournamentId: player.lobbyId,
                        },
                        player.token,
                        player.socketId,
                        player.projectType
                    );

                } else {

                    logger.info("[disconnectHandler] playerList Length2 : ", getTGP.playerList.length);

                    await Lock.getLock().release(locks);

                    await Lock.getLock().release(lock);

                    client.disconnect();

                    await disconnectTimerQueue({ userId, tableId });

                    return;

                }

            }

            await Lock.getLock().release(locks);
            await Lock.getLock().release(lock);
            return;

        }

        client.disconnect();

        const onlineUserData = await getOnlineUser(userId);
        logger.info('[leaveTable] - onlineUserData ::: userDisconnect >>>>', onlineUserData);
        if (onlineUserData) {
            await removeOnlineUser(userId);
            await removeOnlineUserLobbyWise(userId, onlineUserData.lobbyId);
        }

        await Lock.getLock().release(locks);
        await Lock.getLock().release(lock);

        return;


    } catch (error) {
        console.log('[selectedNumberList] Error >> ', error);
    }

}