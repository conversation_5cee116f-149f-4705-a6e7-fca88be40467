export = Object.freeze({
  // Time Out class
  POPUP_TITLE: 'Alert',
  POPUP_TYPE: 'Acknowledge Event',
  //rejoin class
  REJOIN_POPUP_TYPE: 'Acknowledge Event',
  REJOIN_POPUP_MESSAGE: 'success',
  REJOIN_POPUP_TITLE: 'Alert',
  BUTTON_TEXT: {
    YES: 'Yes',
    NO: 'No',
    EXIT: 'Exit',
    REJOIN : 'Rejoin',
    NEW_GAME : 'New Game',
  },
  BUTTON_COLOR: {
    RED: 'red',
    GREEN: 'green',
  },
  BUTTON_METHOD: {
    YES: 'PlayAgainYes',
    NO: 'PlayAgainNo',
    FTUESkipYes: 'FTUESkipYes',
    FTUESkipNo: 'FTUESkipNo',
    EXIT: 'ExitBtnClick',
  },
  TYPE: {
    TOP_TOAST_POPUP: 'topToastPopup',
    TOAST_POPUP: 'toastPopup',
    COMMON_POPUP: 'commonPopup',
    SCOREBOARD_POPUP: 'scoreBoardPopup',
  },
  LOCK_IN_STATE: 'you are already in lock in state',
});
