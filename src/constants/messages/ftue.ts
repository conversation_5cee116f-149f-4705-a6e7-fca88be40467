export = Object.freeze([
  'Spades is a 4 players game played using a standard 52 card deck.',
  'This is Solo game, Spades Masters allows you to play alone.',
  'The aim of the game is to reach the <color=#00e390>target points</color>(avoiding bags). You earn points by winning <color=#00e390>tricks.</color>',
  'Ace is highest, 2 is lowest... and Spades trump all.',
  'A <color=#00e390>TRICK</color> is won by playing the highest card of the same suit.',
  "Let's try a round: First, the dealer will hand each player 13 cards.",
  'Then, each player will look at their cards and bid how many tricks they think they can win.',
  'Looking at your cards. we estimate you can win 6 \n<color=#00e390>TRICKS</color>',
  'You can bid anywhere between Nil and 13. Looking at your cards, let us <color=#00e390>Bid 6.</color>',
  'Play the highest cards of the leading suit (hearts) to win this <color=#00e390>TRICK.</color>\n\nSwipe or tap the A(H) to play it',
  'Great ! You won your first\n<color=#00e390>TRICK!</color>',
  "Now it's your turn to play. Play the K of Clubs card.",
  "You lost that <color=#00e390>TRICK</color> because an Ace beats a king. That's ok, you only need to win 6 to meet the <color=#00e390>BID.</color>",
  "You don't have cards matching the leading suit (Hearts). You can now play any card including the spades suit. Play the 2 of Spades.",
  'Using the first Spade is know as a Spades Break, and now Spades can be used as the leading suit. A Spade will always trump other suits.',
  "Go ahead and finish this game with what you've learned.",
  'You <color=#00e390>BID 6</color> and won bidNumber <color=#00e390>TRICKS</color>; extra <color=#00e390>TRICKS</color> are worth 1 point instead of 10. Extra trick will be added in round bags.',
  'If total bag count exceeds the limit then you will be penalized with -100 points and lose\nthe game.',
  'The game will continue till one of the player reaches the target points or someone\nhas 10 bags.',
]);
