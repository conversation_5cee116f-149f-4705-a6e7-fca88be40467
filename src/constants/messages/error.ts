export = Object.freeze({
  CURRENT_BID_TURN_IS_NOT_YOUR_ERROR_MESSAGES:
    'current Bid turn is not your turn !',
  CURRENT_TURN_IS_NOT_YOUR_ERROR_MESSAGES: 'current turn is not your turn !',
  CURRENT_CARD_IS_NOT_YOUR_ERROR_MESSAGES: 'current card is not your Card !',
  TABLE_NOT_FOUND_ERROR_MESSAGES: 'Table Not Found.',
  DECLARED_WINNER_ERROR_MESSAGES: 'This Table Declared Winner.',
  DONT_THROW_SPADES_ERROR_MESSAGES: `You don't throw spades card !`,
  USER_ID_NOT_FOUND_ERROR_MESSAGES: 'UserId Not Found!',
  USER_DETAIL_NOT_FOUND_ERROR_MESSAGES: 'User Detail Not Found!',
  DONT_THROW_OTHER_ERROR_MESSAGES: `You don't throw other sequebce card !`,
  THROW_THE_HIGHT_CARD_ERROR_MESSAGES: `throw the hight card !`,
  THROW_THE_SPADES_CARD_ERROR_MESSAGES: `throw the spades card !`,
  SCORE_BOARD_NOT_FOUND: `Score Board Not Generated yet`,
  INSUFFICIENT_BALANCE: `Insufficient Balance For This Table`,
  COMMON_ERROR: 'Oops! Something went wrong. Please try again later.',
  RESUFFUL_YOUR_CARDS: `The opposing user has not received a Spade card, so the cards will be reshuffled.`,
  REJOIN_PREVIOUS_TABLE: `Your previous table still running.if you want to join  previous table, please click on REJOIN button.`,
  EXISTING_TABLE_IS_DESTROYED: `The existing table is destroyed before the start of the game. so please exit the current table and join another table from the lobby again!`,
  SERVER_UNDER_THE_MAINTENANCE: `Server under the maintenance.`,
  VARIFY_USER_PROFILE: `varify user profile failed. Please try again later`,
  ENTRY_FEE_DEDUCTED_MSG: `Oops! Something went wrong in entry fee deduction. Please check your balance and try again`,
  MULTIPLE_LOGIN_FAILED_MSG : `Multiple Login failed`
});
