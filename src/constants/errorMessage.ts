export const errorMessages = {
    AUTH_REQUIRED: 'Please login first, and try again',
    SOMETHING_WENT_WRONG: 'Something went wrong!',
    INVALID_INPUT: 'Validation in Error : INVALID_INPUT',
    REGISTER_SUCCESS: `Registred Successful!`,
    ALLREADY_IN_PLAY: 'You are allready in play mode!',
    WAIT_DONOT_LEAVE: 'Game is allready in waitingTag state, so do not leave table',
    WIINING_DONOT_LEAVE: 'Game is allready in winning state, so do not leave table',
    LOW_BALANCE: 'Available chip is low!',
    SERVER_ERROR: 'The server has encountered a situation it does not know how to handle.',
    SUCCESS: 'Success',
    USER_NOT_FOUND: 'Player not found!',
    GAME_START: 'GT - Game Start',
    NOT_YOUR_TURN: `Wait it's not your turn!`,
    INVALID_POOL_BALL: 'Given a Ball is allready potted!, What happend in your mind',
    DISCONNECT_PLAYER: 'Player has been disconnected',
    JOIN_TABLE: 'Player Join table Success',
    COLLECT_ENTRY_FEES: `Both player's enterfees have been collected`,
    PLAYER_NOT_FOUND: 'Player Data Not Found',
    PGP_NOT_FOUND: 'Player Game Play Data Not Found',
    TGP_NOT_FOUND: 'Table Game Play Data Not Found',
    REJOIN_TGP_NOT_FOUND: 'TGP Not found In Rejoin Senario, Please Start New Game',
    REJOIN_UPDATE_TGP_NOT_FOUND: 'UPDATE TGP Not found In Rejoin Senario, Please Start New Game',
    SOCKET_INVALID_TABLEID: 'Invalid or  Not Found Table ID, Please Check whats your Issue?',
    SOCKET_INVALID_PLAYERID: 'Invalid or  Not Found Player ID, Please Check whats your Issue?',
    ENTRY_FEE_DEDUCTED_MSG: `Oops! Something went wrong in entry fee deduction. Please check your balance and try again`,
    SERVER: 'Server under the maintenance!',
    NOT_DEDUCT_ENTRYFEE: 'The API side does not deduct the entry fee, so the game is over.',
    CHEASSBOARD_ERROR: 'Create ChessBoard data is not found',
    MISSMATCH: 'Does not match the last position (Please send proper Data)',
}