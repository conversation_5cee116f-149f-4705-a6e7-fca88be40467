import { T_TABLE_STATE, T_PLAYER_STATE, T_TICKET_TYPE } from "../interface/common";
import MESSAGES from "./messages";
import NUMERICAL from "./numerical";

export const CONSTANTS = {

    EVENTS: {
        // REQUEST
        HB: 'HB',
        SIGN_UP: 'SIGN_UP',
        LEAVE_TABLE: 'LEAVE_TABLE',
        SELECT_NUMBER: 'SELECT_NUMBER',
        PLAYERLIST: 'PLAYERLIST',
        USERLIST: 'USERLIST',
        NUMBER_LIST: 'NUMBER_LIST',
        PATTERN: 'PATTERN',
        CLAIM_COUNT: 'CLAIM_COUNT',
        EARLY_FIVE: 'EARLY_FIVE',
        TOP_LINE: 'TOP_LINE',
        MIDDLE_LINE: 'MIDDLE_LINE',
        BOTTOM_LINE: 'BOTTOM_LINE',
        CORNERS: 'CORNERS',
        FULL_HOUSE: 'FULL_HOUSE',
        CLAIM_TOUCH: 'CLAIM_TOUCH',

        // RESPONSE
        RE_JOIN: 'RE_JOIN',
        COLLECT_ENTRYFEE: 'COLLECT_ENTRYFEE',
        SELECT_TABLE: 'SELECT_TABLE',
        JOIN_TABLE: 'JOIN_TABLE',
        GAME_START: 'GAME_START',
        ERROR_POPUP: 'ERROR_POPUP',
        NO_PLAYER: 'NO_PLAYER',
        WINNING: 'WINNING',
        WINNING_ACK: 'WINNING_ACK',
        RANDOM_BALL: 'RANDOM_BALL',
        ERROR_TICKET: 'ERROR_TICKET',
        LAST_GAME: 'LAST_GAME',
        WINORLOSS: 'WINORLOSS',
        ALERT: 'ALERT',
    },

    REDIS: {
        EMPTY_TABLE: 'EMPTY_TABLE',
        MATCHMAKING: 'MATCHMAKING',
        PLAYER: 'PLAYER',
        PLAYER_GAME_PLAY: 'PGP',
        TABLE_GAME_PLAY: 'TGP',
        ONLINE_PLAYER: 'ONLINE_PLAYER',
        ONLINE_PLAYER_LOBBY_WISE: 'ONLINE_PLAYER_LOBBY_WISE',
        DELETE_REDIS_DATA: 7200,
        WINNER_LIST: 'WINNER_LIST',
    },

    ENVIRONMENT: {
        LOCAL: 'LOCAL',
        STANDALONE: 'STANDALONE',
        DEVELOPMENT: 'DEVELOPMENT',
        STAGE: 'STAGE',
        PRODUCTION: 'PRODUCTION',
        DEMO_PRODUCTION: 'DEMO_PRODUCTION',
    },

    REDLOCK: {
        LOCK: 'LOCK',
        LEAVE_GAME: 'LEAVE_GAME',
        PATTERN: 'PATTERN',
        RANDOMBALL: 'RANDOMBALL',
    },

    ERROR: {
        REQUEST_HANDLER_VALIDATION: 'Request Handler Request Data Not Valid !',
        SIGNUP_VALIDATION: 'Signup Request Data Not Valid !',
        TIKET_PATTERN_VALIDATION: 'Pattern Request Data Not Valid !',
        CLAIM_TOUCH_VALIDATION: 'Claimed Pattern Request Data Not Valid !',
        USER_NOT_CREATED: 'User Not Create during signup !',
        USER_NOT_FOUND: 'Redis Database In User Not Found!',
        MATCHMAKING_VALIDATION: 'MatchMaking Request Data Not Valid !',
        ENTRYFEE_RESPONSE: 'The response for the entry fee from the API side was not found !',
        ENTRYFEE_INSUFFICIANT_BALANCE: 'API side some users have insufficient balance !',
        ENTRYFEE_NOT_DEDUCT: 'API side due to the entry fee not being deducted !',
        ENTRYFEE_NOT_MINIMUM_PLAYER: "Minimum player's entryfee is not deducted, and a socket disconnects, the game is considered over.",
        SELECT_NUMBER_VALIDATION: 'SELECT_NUMBER Event Request Data Not Valid !',
        SELECT_NUMBER_ERROR: 'The selected number is not present on the original ticket.',
        WRONG_TICKET_NUMBER: "The ticket number is not present in the user's tickets.",
        LEAVE_VALIDATION: 'LeaveTable Request Socket Data Not Found !',
        PLAYERLIST_VALIDATION: 'PlayerList Request Socket Data Not Found !',
        WINNING_ACK_VALIDATION: 'WinningAck Request Socket Data Not Found !',
        DO_NOT_LEAVE: 'Players must not leave the game during the entry fee deduction and winning phase.',
        LEAVE_ACTIVE_PLAYER_ERROR: '[leave] Active player is not present in the player list during gameplay.',
        DISCONNECT_ACTIVE_PLAYER_ERROR: '[disconnect] Active player is not present in the player list during gameplay.',
        NUMBER_SHUFFLE_ERROR: 'The shuffling of ball numbers between 1 and 90 is not functioning correctly.',
        ALL_PATTERN_VALIDATION: 'All Pattern Request Socket Data Not Found !',
        LAST_GAME: `The Last Game That You Played Doesn't Exist Anymore.`,

        CORNERS_ERROR: "Corners Invalid. Minimum 4 Numbers Select On Ticket !",
        CORNERS_ERROR_2: "Corners Invalid. All The 4 Numbers Of The Corners Have Not Been Selected.",

        EARLYFIVE_ERROR: "EarlyFive Invalid. Select a Minimum Of Five Valid Numbers From The Ticket !",
        EARLYFIVE_ERROR_2: "EarlyFive Invalid. All The First 5 Numbers Have Not Been Selected.",

        TOPLINE_ERROR: "TopLine Invalid. Minimum 5 Numbers Select On Ticket !",
        TOPLINE_ERROR_2: "TopLine Invalid. All The First Line 5 Numbers Have Not Been Selected.",

        MIDDLELINE_ERROR: "MiddleLine Invalid. Minimum 5 Numbers Select On Ticket !",
        MIDDLELINE_ERROR_2: "MiddleLine Invalid. All The Second Line 5 Numbers Have Not Been Selected.",

        BOTTOMLINE_ERROR: "BottomLine Invalid. Minimum 5 Numbers Select On Ticket !",
        BOTTOMLINE_ERROR_2: "BottomLine Invalid. All The Third Line 5 Numbers Have Not Been Selected.",

        FULLHOUSE_ERROR: "FullHouse Invalid. Minimum 15 Numbers Select On Ticket !",
        FULLHOUSE_ERROR_2: "FullHouse Invalid. All The 15 Numbers Have Not Been Selected.",

        REQUEST_SWITCHCASE_ERROR: "Invalid Request Listner Name !!",
    },

    RESPONSE: {
        REGISTER_SUCCESS: "Registration Successful!",
        CREATE_TABLE: "Table successfully created !",
        JOIN_TABLE: "Table successfully joined !",
        UPDATE_JOIN_TABLE : "Join Table successfully updated !",
        USERLIST_SUCCESS: "Show All Players List !",
        ENTRYFEE_ISSUE_JOIN_TABLE: "EntryFee Deduction Time Re Join Table Event Sent !",
        NO_PLAYER_FOUND: "No players found at the moment.",
        NO_PLAYER_MESSAGE: "No player found. Please select 'retry' or 'exit'.",
        GAME_START: "Game Start !",
        REJOIN_SUCCESS: "Rejoin successful.",
        RANDOM_BALL: "Random ball generation successful.",
        EARLY_FIVE_SUCCESS: "Early Five ticket has been claimed.",
        TOP_LINE_SUCCESS: "Top Line ticket has been claimed.",
        MIDDLE_LINE_SUCCESS: "Middle Line ticket has been claimed.",
        BOTTOM_LINE_SUCCESS: "Bottom Line ticket has been claimed.",
        CORNERS_SUCCESS: "Corners ticket has been claimed.",
        FULL_HOUSE_SUCCESS: "Full House ticket has been claimed.",
        CLAIM_PATTERN_SUCCESS: "All Tickets Claimed Updated.",
        REJOIN_PATTERN_SUCCESS: "Rejoin All ticket has been updated.",
        WINNING_SUCCESS: "Winner Successful!",
        NUMBER_SELECT: 'Number Select Successfully.'
    },

    BULL: {
        MATCHMAKING_BULL: 'MATCHMAKING_BULL',
        COLLECT_ENTRY_FEE: 'COLLECT_ENTRY_FEE',
        GAME_START_BULL: 'GAME_START_BULL',
        GAME_START_EXTEND_BULL: 'GAME_START_EXTEND_BULL',
        DISCONNECT_BULL: 'DISCONNECT_BULL',
        RANDOM_BALL_BULL: 'RANDOM_BALL_BULL',
        DELAY_WINNING_BULL: 'DELAY_WINNING_BULL',
    },

    STATE: {
        INITIAL: "INITIAL" as T_PLAYER_STATE,
        PLAYING: "PLAYING" as T_PLAYER_STATE,
        LEAVE: "LEAVE" as T_PLAYER_STATE,
        DISCONNECT: "DISCONNECT" as T_PLAYER_STATE,

        WAITING_FOR_PLAYER: "WAITING_FOR_PLAYER" as T_TABLE_STATE,
        GAME_START_TIMER: "GAME_START_TIMER" as T_TABLE_STATE,
        COLLECT_ENTRY_FEE: "COLLECT_ENTRY_FEE" as T_TABLE_STATE,
        PLAY: "PLAY" as T_TABLE_STATE,
        WIN: "WIN" as T_TABLE_STATE,
    },

    SCORE: {
        FULL_HOUSE_SCORE: 1000,
        TOP_LINE_SCORE: 600,
        MIDDLE_LINE_SCORE: 600,
        BOTTOM_LINE_SCORE: 600,
        EARLY_FIVE_SCORE: 400,
        CORNERS_SCORE: 200,
    },

    GAME: {
        CONTINUE: 'CONTINUE',
        CLOSE: 'CLOSE',
        FULL_HOUSE: 'FULL_HOUSE' as T_TICKET_TYPE,
        TOP_LINE: 'TOP_LINE' as T_TICKET_TYPE,
        MIDDLE_LINE: 'MIDDLE_LINE' as T_TICKET_TYPE,
        BOTTOM_LINE: 'BOTTOM_LINE' as T_TICKET_TYPE,
        EARLY_FIVE: 'EARLY_FIVE' as T_TICKET_TYPE,
        CORNERS: 'CORNERS' as T_TICKET_TYPE,
        WINNER: 'WINNER',
        LOOSER: 'LOOSER',
    },

    SOCKET: {
        CONNECTION: 'connection',
        DISCONNECT: 'disconnect',
    },

    PROJECT_STATE: {
        MGP1: "MGP1",
        MGP2: "MGP2",
        VM: "virtualMoney"
    }

}

export const exportObject = Object.freeze({
    POPUP_TITLE: { ALERT: "Alert" },
    EVENTS: {
        ERROR_SOCKET_EVENT: "ERROR",
        GAME_SETTING_MENU_HELP: ''
    },
    MESSAGES,
    NUMERICAL,
});