
import logger from "../logger";
import { getconfig } from "../config";
import { CONSTANTS } from "../constants";
import { projectTypeInterface } from "../interface/config";
const { PLATFORM } = getconfig();

export const projectTypeManage = async (projectType: string): Promise<projectTypeInterface | undefined> => {
    try {
        logger.info("projectTypeManage >>>>>>>>>> :: ", { projectType, PLATFORM });
        let projectUrlObj: projectTypeInterface;
        const API_BASE_URL_MGP2 = "";
        const API_BASE_URL_MGP = PLATFORM.API_BASE_URL_MGP;
        const API_BASE_URL_VM = PLATFORM.API_BASE_URL_VM;

        if (projectType === CONSTANTS.PROJECT_STATE.MGP2) {
            projectUrlObj = {
                VERIFY_USER_PROFILE: String(`${API_BASE_URL_MGP2}/gameServerApi/checkIsValidToken`),
                GET_USER_OWN_PROFILE: String(`${API_BASE_URL_MGP2}/gameServerApi/getUsersOwnProfile`),
                DEDUCT_USER_ENTRY_FEE: String(`${API_BASE_URL_MGP2}/gameServerApi/deductEntryFee`),
                MULTI_PLAYER_SUBMIT_SCORE: String(`${API_BASE_URL_MGP2}/gameServerApi/multiPlayerSubmitScore`),
                GAME_SETTING_MENU_HELP: String(`${API_BASE_URL_MGP2}/gameServerApi/getGameRules`),
                MARK_COMPLETED_GAME_STATUS: String(`${API_BASE_URL_MGP2}/gameServerApi/markCompletedGameStatus`),
                GET_ONE_ROBOT: String(`${API_BASE_URL_MGP2}/gameServerApi/getBot`),
                CHECK_BALANCE: String(`${API_BASE_URL_MGP2}/gameServerApi/checkBalance`),
                REDIUS_CHECK: String(`${API_BASE_URL_MGP2}/gameServerApi/getRadiusLocation`),
                FTUE_UPDATE: String(`${API_BASE_URL_MGP2}/gameServerApi/userFirstTimeIntrection`),
                CHECK_USER_BLOCK_STATUS: String(`${API_BASE_URL_MGP2}/gameServerApi/checkUserBlockStatus`),
                CHECK_MAINTANENCE: String(`${API_BASE_URL_MGP2}/gameServerApi/checkMaintanence`),
                ADD_GAME_RUNNING_STATUS: String(`${API_BASE_URL_MGP2}/gameServerApi/addRunningGameStatus`),
                MULTI_PLAYER_DEDUCT_ENTRY_FEE: String(`${API_BASE_URL_MGP2}/gameServerApi/multiPlayerDeductEntryFee`),
            };
        } else if (projectType === CONSTANTS.PROJECT_STATE.VM) {
            projectUrlObj = {
                VERIFY_USER_PROFILE: String(`${API_BASE_URL_VM}/gameServerApi/checkIsValidToken`),
                GET_USER_OWN_PROFILE: String(`${API_BASE_URL_VM}/gameServerApi/getUsersOwnProfile`),
                DEDUCT_USER_ENTRY_FEE: String(`${API_BASE_URL_VM}/gameServerApi/deductEntryFee`),
                MULTI_PLAYER_SUBMIT_SCORE: String(`${API_BASE_URL_VM}/gameServerApi/multiPlayerSubmitScore`),
                GAME_SETTING_MENU_HELP: String(`${API_BASE_URL_VM}/gameServerApi/getGameRules`),
                MARK_COMPLETED_GAME_STATUS: String(`${API_BASE_URL_VM}/gameServerApi/markCompletedGameStatus`),
                GET_ONE_ROBOT: String(`${API_BASE_URL_VM}/gameServerApi/getBot`),
                CHECK_BALANCE: String(`${API_BASE_URL_VM}/gameServerApi/checkBalance`),
                REDIUS_CHECK: String(`${API_BASE_URL_VM}/gameServerApi/getRadiusLocation`),
                FTUE_UPDATE: String(`${API_BASE_URL_VM}/gameServerApi/userFirstTimeIntrection`),
                CHECK_USER_BLOCK_STATUS: String(`${API_BASE_URL_VM}/gameServerApi/checkUserBlockStatus`),
                CHECK_MAINTANENCE: String(`${API_BASE_URL_VM}/gameServerApi/checkMaintanence`),
                ADD_GAME_RUNNING_STATUS: String(`${API_BASE_URL_VM}/gameServerApi/addRunningGameStatus`),
                MULTI_PLAYER_DEDUCT_ENTRY_FEE: String(`${API_BASE_URL_VM}/gameServerApi/multiPlayerDeductEntryFee`),
            };
        } else {
            projectUrlObj = {
                VERIFY_USER_PROFILE: String(`${API_BASE_URL_MGP}/gameServerApi/checkIsValidToken`),
                GET_USER_OWN_PROFILE: String(`${API_BASE_URL_MGP}/gameServerApi/getUsersOwnProfile`),
                DEDUCT_USER_ENTRY_FEE: String(`${API_BASE_URL_MGP}/gameServerApi/deductEntryFee`),
                MULTI_PLAYER_SUBMIT_SCORE: String(`${API_BASE_URL_MGP}/gameServerApi/multiPlayerSubmitScore`),
                GAME_SETTING_MENU_HELP: String(`${API_BASE_URL_MGP}/gameServerApi/getGameRules`),
                MARK_COMPLETED_GAME_STATUS: String(`${API_BASE_URL_MGP}/gameServerApi/markCompletedGameStatus`),
                GET_ONE_ROBOT: String(`${API_BASE_URL_MGP}/gameServerApi/getBot`),
                CHECK_BALANCE: String(`${API_BASE_URL_MGP}/gameServerApi/checkBalance`),
                REDIUS_CHECK: String(`${API_BASE_URL_MGP}/gameServerApi/getRadiusLocation`),
                FTUE_UPDATE: String(`${API_BASE_URL_MGP}/gameServerApi/userFirstTimeIntrection`),
                CHECK_USER_BLOCK_STATUS: String(`${API_BASE_URL_MGP}/gameServerApi/checkUserBlockStatus`),
                CHECK_MAINTANENCE: String(`${API_BASE_URL_MGP}/gameServerApi/checkMaintanence`),
                ADD_GAME_RUNNING_STATUS: String(`${API_BASE_URL_MGP}/gameServerApi/addRunningGameStatus`),
                MULTI_PLAYER_DEDUCT_ENTRY_FEE: String(`${API_BASE_URL_MGP}/gameServerApi/multiPlayerDeductEntryFee`),
            };
        }

        return projectUrlObj;

    } catch (error: any) {
        logger.info("[projectTypeManage] >> Error >> ", error)
        return undefined;
    }
}


