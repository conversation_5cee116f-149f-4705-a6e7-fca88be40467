import { CONSTANTS } from "../constants";
import { configReturn } from "../interface/config";
import { getLocalConfig } from "./local";
import { getServerConfig } from "./server";
import 'dotenv/config';


export const getconfig = (): configReturn => {

    if ((process.env.ENVIRONMENT)?.toUpperCase() === CONSTANTS.ENVIRONMENT.LOCAL) {
        return getLocalConfig();
    }
    else {
        return getServerConfig();
    }

};