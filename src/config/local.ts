import { configReturn } from "../interface/config";
import 'dotenv/config';

export const getLocalConfig = (): configReturn => {

    const {

        ENVIRONMENT,

        REDIS_HOST,
        REDIS_PORT,
        REDIS_PASSWORD,
        REDIS_DATABASE_NUMBER,
        PUBSUB_REDIS_HOST,
        PUBSUB_REDIS_PORT,
        PUBSUB_REDIS_PASSWORD,

        SERVER_PORT,
        CERTIFICATE_CERT_PATH,
        CERTIFICATE_KEY_PATH,

        APP_KEY,
        APP_DATA,
        SECRET_KEY,
        GAME_ID,
        API_BASE_URL_MGP,
        API_BASE_URL_VM,

        RANGE_OF_RADIUS,
    } = process.env;

    return {

        ENVIRONMENT: ENVIRONMENT ? ENVIRONMENT : '',

        REDIS: {
            REDIS_HOST: REDIS_HOST ? REDIS_HOST : '',
            REDIS_PORT: REDIS_PORT ? Number(REDIS_PORT) : 0,
            REDIS_PASSWORD: REDIS_PASSWORD ? REDIS_PASSWORD : '',
            REDIS_DATABASE_NUMBER: REDIS_DATABASE_NUMBER ? Number(REDIS_DATABASE_NUMBER) : 0,
            PUBSUB_REDIS_HOST: PUBSUB_REDIS_HOST ? PUBSUB_REDIS_HOST : '',
            PUBSUB_REDIS_PORT: PUBSUB_REDIS_PORT ? Number(PUBSUB_REDIS_PORT) : 0,
            PUBSUB_REDIS_PASSWORD: PUBSUB_REDIS_PASSWORD ? PUBSUB_REDIS_PASSWORD : '',
        },

        LOGGER: {
            LOG: false,
        },

        SERVER_CONFIG: {
            SERVER_PORT: SERVER_PORT ? Number(SERVER_PORT) : 0,
            CERTIFICATE_CERT_PATH: CERTIFICATE_CERT_PATH ? CERTIFICATE_CERT_PATH : '',
            CERTIFICATE_KEY_PATH: CERTIFICATE_KEY_PATH ? CERTIFICATE_KEY_PATH : '',
        },

        PLATFORM: {
            APP_KEY: APP_KEY ? APP_KEY : '',
            APP_DATA: APP_DATA ? APP_DATA : '',
            SECRET_KEY: SECRET_KEY ? SECRET_KEY : '',
            GAME_ID: GAME_ID ? GAME_ID : '',
            API_BASE_URL_MGP: API_BASE_URL_MGP ? API_BASE_URL_MGP : '',
            API_BASE_URL_VM: API_BASE_URL_VM ? API_BASE_URL_VM : '',
        },

        GAMEPLAY: {
            PLATFORM_RAKE : 10, // In Percentage
            RANGE_OF_RADIUS: RANGE_OF_RADIUS ? Number(RANGE_OF_RADIUS) : 0,
            MIN_PLAYER: 2,
            MAX_PLAYER: 100,
            MIN_TICKET: 1,
            MAX_TICKET: 20,
            TOTAL_TICKET: 100,
            MATCH_MAKING_TIMER: 30,

            FULL_HOUSE_PERCENTAGE: 30,
            TOP_LINE_PERCENTAGE: 16,
            MIDDLE_LINE_PERCENTAGE: 16,
            BOTTOM_LINE_PERCENTAGE: 16,
            EARLY_FIVE_PERCENTAGE: 10,
            CORNERS_PERCENTAGE: 12,

            FULL_HOUSE_PERSON: 1,
            TOP_LINE_PERSON: 2,
            MIDDLE_LINE_PERSON: 2,
            BOTTOM_LINE_PERSON: 2,
            EARLY_FIVE_PERSON: 2,
            CORNERS_PERSON: 2,

            GAME_START_TIMER: 1,
            DISCONNECT_TIMER: 10,
            RANDOM_BALL_TIMER: 0.2,
            GAME_START_DELAY_TIMER: 1,
            WINNING_DELAY_TIMER: 2,
        }

    }

}