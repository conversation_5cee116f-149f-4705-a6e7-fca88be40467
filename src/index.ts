import 'dotenv/config';
import { httpsConnection } from "./connection/https";
import { redisConnection } from "./connection/redis";
import { Lock } from "./connection/redlock";
import { socketConnection } from "./connection/socket";
import { startEventListner } from "./playing/events/eventListners";


(async () => {

    await redisConnection();
    await httpsConnection();
    await socketConnection();
    await startEventListner();

})().catch((error: any) => {
    console.log("[Connection_Index] Catch Error :: ", error);
});

Lock.init();


process
    .on('unhandledRejection', (response, p) => {
        console.log('Unhandled Rejection at Promise >> ', new Date(), " :: ", response);
        console.log(p);
    })
    .on('uncaughtException', (err) => {
        console.log('Uncaught Exception thrown >> ', new Date(), " :: ", err);
    });