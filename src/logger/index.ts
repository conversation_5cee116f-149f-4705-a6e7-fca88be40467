import winston, { <PERSON><PERSON> } from 'winston';
import { getconfig } from '../config';

const utcDate = new Date();

const istDate = new Intl.DateTimeFormat('en-IN', {
    timeZone: 'Asia/Kolkata',
    year: 'numeric', month: 'numeric', day: 'numeric',
    hour: 'numeric', minute: 'numeric', second: 'numeric',
}).format(utcDate);

const IND_Time = `${istDate} ${utcDate.getMilliseconds().toString().padStart(3, '0')}`;

const winstonLogger: Logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.json(),
        // winston.format.timestamp({ format: 'DD-MM-YYYY HH:mm:ss.SSS' }),
        winston.format.printf(info => `${IND_Time} : ${info.level} >> ${info.message}`)
    ),
    transports: [new winston.transports.Console()]
});

const infoLogger = async (path: string, data: any): Promise<void> => {
    try {
        const CONFIG = getconfig();

        if (CONFIG.LOGGER.LOG) {
            if (typeof data !== 'string') data = JSON.stringify(data);
            winstonLogger.info(`${path} ${data}`);
            console.log("");
        }
    } catch (error) {
        console.error('[Logger] Catch Error:', error);
    }
};

const logger = {
    info: async (path: string, data: any): Promise<void> => {
        await infoLogger(path, data);
    }
};

export default logger;
