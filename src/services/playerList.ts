import { Socket } from "socket.io";
import { Emitter } from "../playing/events/eventEmmiter";
import { CONSTANTS } from "../constants";
import { Lock } from "../connection/redlock";
import { getTableGamePlay } from "../playing/database/table";
import logger from "../logger";
import { genratePlayerList, genrateShowAllUserList } from "../playing/helper/genratePlayerList";


export const playerList = async (
    data: {},
    client: Socket,
): Promise<void> => {

    const userId = client.handshake.auth.id;
    const tableId = client.handshake.auth?.tableId;

    const redlockId = `${CONSTANTS.REDLOCK.LOCK}:${CONSTANTS.REDIS.PLAYER_GAME_PLAY}:${userId}:${tableId}`;
    const playerListLock = await Lock.getLock().acquire([redlockId], 2000);

    try {

        if (!userId || !tableId) {
            logger.info(`Error : ${CONSTANTS.ERROR.PLAYERLIST_VALIDATION}`, { userId, tableId });
            throw new Error(CONSTANTS.ERROR.PLAYERLIST_VALIDATION);
        }

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        // const playerList = await genratePlayerList(tableId, getTGP.playerList.length, "");
        const playerList = await genrateShowAllUserList(tableId);
        if (!playerList) { throw new Error("[playerList] playerList Not Found !"); }

        if (playerList && playerList.length > 0) {

            Emitter.emit(CONSTANTS.EVENTS.PLAYERLIST, {
                sentId: client.id,
                data: { list: playerList },
                message: `playerList event successful : ${tableId}.`,
            });

        }

    } catch (error: any) {
        console.log("playerList Error : ", error);
        Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
            sentId: client.id,
            data: {
                error: true,
                message: `${error}`
            },
            message: `[playerList] Function Error !`,
        });
    } finally {
        await Lock.getLock().release(playerListLock);
    }

}