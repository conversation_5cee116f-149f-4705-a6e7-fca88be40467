import { Socket } from "socket.io";
import { CONSTANTS } from "../constants";
import { Lock } from "../connection/redlock";
import { getTableGamePlay } from "../playing/database/table";
import { Emitter } from "../playing/events/eventEmmiter";


export const selectedNumberList = async (
    data: {},
    client: Socket,
): Promise<void> => {

    const tableId = client.handshake.auth.tableId;
    const redlockId = `${CONSTANTS.REDLOCK.LOCK}:${CONSTANTS.REDIS.TABLE_GAME_PLAY}:${tableId}`;
    const selectNumberLock = await Lock.getLock().acquire([redlockId], 2000);

    try {

        if (!tableId) { throw new Error("[selectedNumberList] TableId Not Found !!"); }

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("get TableGamePlay Data Failed!"); }

        Emitter.emit(CONSTANTS.EVENTS.NUMBER_LIST, {
            sentId: client.id,
            data: { numbers: getTGP.randomNumbers },
            message: `Selected Number List Response.`,
        });

        return;

    } catch (error) {
        console.log('[selectedNumberList] Error >> ', error);
        Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
            sentId: client.id,
            data: {
                error: true,
                message: `${error}`
            },
            message: `[selectedNumberList] Function Error !`,
        });

    } finally {

        await Lock.getLock().release(selectNumberLock);
    }

} 