import { Socket } from "socket.io";
import { CONSTANTS } from "../constants";
import logger from "../logger";
import { Lock } from "../connection/redlock";
import { REQUEST_SIGNUP } from "../interface/event";
import { signupInputValidator } from "../playing/validations";
import { Emitter } from "../playing/events/eventEmmiter";
import { getOnlineUser, removeOnlineUser, removeOnlineUserLobbyWise, setOnlineUser, setOnlineUserLobbyWise } from "../playing/database/onlineStatus";
import { createNewPlayer, findUser, getPlayer, updatePlayer } from "../playing/database/player";
import { I_MATCH_MAKING, I_REJOIN } from "../interface/common";
import { matchMaking } from "../playing/controller/matchMaking/matchmaking";
import { getTableGamePlay, updateTableGamePlay } from "../playing/database/table";
import { rejoin } from "../playing/controller/reJoin/rejoin";
import { getPlayerGamePlay, updatePlayerGamePlay } from "../playing/database/pgp";
import { checkMaintanence } from "../playing/clientSideApi/checkMaintanence";
import { checkBalance, firstTimeIntrection, verifyUserProfile } from "../playing/clientSideApi";
import { checkBalanceIf } from "../interface/cmgApiIf";
import userProfileUpdate from "../playing/controller/signup/userProfile";


export const signup = async (
    data: REQUEST_SIGNUP,
    client: Socket,
) => {

    const MatchMakingId = `${CONSTANTS.REDLOCK.LOCK}:${CONSTANTS.REDIS.EMPTY_TABLE}:${data?.entryFee}`;
    const MatchMakingLock = await Lock.getLock().acquire([MatchMakingId], 2000);

    try {

        // text to number
        if (typeof data.ticket === 'string') {
            const res = data.ticket.split(" ")[0]
            data.ticket = Number(res);
        }

        const error = await signupInputValidator(data);
        if (error) {
            logger.info(`Error : ${CONSTANTS.ERROR.SIGNUP_VALIDATION}`, "");
            throw new Error(`${error}`);
        }

        client.handshake.auth.id = data.userId;

        // ! set API Port and return All API url
        client.handshake.auth.projectType = data.projectType;

        // ! check server in Maintanence API
        let checkMaintanenceData = await checkMaintanence(data.token, client.id, data.userId, data.projectType);
        logger.info("checkMaintanenceData :::", { userId: data.userId, checkMaintanenceData });
        if (checkMaintanenceData && checkMaintanenceData.isMaintenance) {
            throw new Error(`Server under the maintenance!`);
        }

        // ! Check auth token is valid or not API
        let isValidUserData = await verifyUserProfile(data.token, client.id, data.projectType);
        logger.info("isValidUserData :: >> ", isValidUserData);

        const onlineUserData = {
            userId: data.userId,
            lobbyId: data.lobbyId
        }
        await setOnlineUser(data.userId, onlineUserData);
        await setOnlineUserLobbyWise(data.userId, data.lobbyId, { userId: data.userId });

        // ! First Time interaction API
        if (data.isFTUE) {
            await firstTimeIntrection(data.gameId, data.gameModeId, data.token, client.id, data.projectType);
            data.isFTUE = false;
        }

        let AvailablePlayer = await findUser(data.userId);
        if (AvailablePlayer) {

            if (AvailablePlayer.tableId === "") {

                // ! check Balance API
                const balanceData: checkBalanceIf = {
                    tournamentId: data.lobbyId
                }
                let checkBalanceDetail = await checkBalance(balanceData, data.token, client.id, data.projectType);
                logger.info("checkBalanceDetail ==>>>", checkBalanceDetail);
                if (checkBalanceDetail && checkBalanceDetail.userBalance.isInsufficiantBalance) {
                    logger.info("isInsufficiantBalance ::", checkBalanceDetail.userBalance.isInsufficiantBalance);
                    throw new Error(`"Insufficient Balance!"`);
                }
            }

            AvailablePlayer.socketId = client.id;
            AvailablePlayer.userName = data.userName;
            AvailablePlayer.userProfile = data.userProfile;
            AvailablePlayer.token = data.token;
            AvailablePlayer.lobbyId = data.lobbyId;
            AvailablePlayer.gameId = data.gameId;
            AvailablePlayer.isFTUE = data.isFTUE;
            AvailablePlayer.entryFee = data.entryFee;
            AvailablePlayer.ticket = data.ticket;
            AvailablePlayer.projectType = data.projectType;
            AvailablePlayer.gameModeId = data.gameModeId;
            AvailablePlayer = await updatePlayer(AvailablePlayer);
            if (!AvailablePlayer) { throw new Error('Update Player Data Not Found !'); }

            // ! user Profile update API
            await userProfileUpdate(AvailablePlayer, client.id, data);

            logger.info("TableId :: ", AvailablePlayer.tableId);

            if (AvailablePlayer.tableId === "") {

                let getPGP = await getPlayerGamePlay(AvailablePlayer.userId, AvailablePlayer.tableId);
                if (getPGP) {
                    getPGP.playerState = CONSTANTS.STATE.INITIAL;
                    await updatePlayerGamePlay(getPGP);
                }

                Emitter.emit(CONSTANTS.EVENTS.SIGN_UP, {
                    sentId: client.id,
                    data: AvailablePlayer,
                    message: CONSTANTS.RESPONSE.REGISTER_SUCCESS,
                });

                const Tabledata: I_MATCH_MAKING = {
                    entryFee: data.entryFee,
                    ticket: data.ticket,
                    userId: data.userId,
                }

                await matchMaking(Tabledata, client);

                return;

            } else {

                const tableId = AvailablePlayer.tableId;
                client.handshake.auth.tableId = tableId;

                let getTGP = await getTableGamePlay(tableId);
                let getPGP = await getPlayerGamePlay(data.userId, tableId);

                logger.info("isPlayerLastGameIsOver : ", { isDisconnect: getPGP?.isDisconnect, isLeave: getPGP?.isLeave });

                if (getTGP && getPGP && !getPGP.isDisconnect && !getPGP.isLeave) {

                    logger.info("Rejoin Start.", "");

                    const findAndUpdate = getTGP.playerList.find((player) => player.userId === data.userId);
                    if (findAndUpdate) {
                        findAndUpdate.userProfile = AvailablePlayer.userProfile;
                        findAndUpdate.userName = AvailablePlayer.userName;
                    }

                    getTGP = await updateTableGamePlay(getTGP);
                    if (!getTGP) { throw new Error('Update TableGamePlay Data Not Found !'); }

                    const rejoinData: I_REJOIN = { userId: data.userId, tableId };

                    await rejoin(rejoinData, client);

                    return;

                } else {

                    logger.info("Last Game Does Not Exits AnyMore.", "");

                    const AlertResData = { Message: CONSTANTS.ERROR.LAST_GAME };

                    client.leave(tableId);

                    AvailablePlayer.tableId = "";

                    AvailablePlayer = await updatePlayer(AvailablePlayer);
                    if (!AvailablePlayer) { throw new Error('Update Player Data Not Found !'); }

                    Emitter.emit(CONSTANTS.EVENTS.LAST_GAME, {
                        sentId: client.id,
                        data: AlertResData,
                        message: 'Last Game Completed'
                    });

                    const onlineUserData = await getOnlineUser(data.userId);
                    if (onlineUserData) {
                        await removeOnlineUser(data.userId);
                        await removeOnlineUserLobbyWise(data.userId, onlineUserData.lobbyId);
                    }

                    return;
                }
            }

        } else {

            let NewPlayer = await createNewPlayer(data, client.id);
            if (!NewPlayer) { throw new Error(CONSTANTS.ERROR.USER_NOT_FOUND); }

            // ! check Balance API
            const balanceData: checkBalanceIf = {
                tournamentId: data.lobbyId
            }
            let checkBalanceDetail = await checkBalance(balanceData, data.token, client.id, data.projectType);
            logger.info("checkBalanceDetail ==>>>", checkBalanceDetail);
            if (checkBalanceDetail && checkBalanceDetail.userBalance.isInsufficiantBalance) {
                logger.info("isInsufficiantBalance ::", checkBalanceDetail.userBalance.isInsufficiantBalance);
                throw new Error(`"Insufficient Balance!"`);
            }

            // ! user Profile update API
            await userProfileUpdate(NewPlayer, client.id, data);

            NewPlayer = await getPlayer(NewPlayer.userId);
            if (!NewPlayer) {
                throw new Error(`"Insufficient Balance!"`);
            }

            Emitter.emit(CONSTANTS.EVENTS.SIGN_UP, {
                sentId: client.id,
                data: NewPlayer,
                message: CONSTANTS.RESPONSE.REGISTER_SUCCESS,
            });

            const Tabledata: I_MATCH_MAKING = {
                entryFee: data.entryFee,
                ticket: data.ticket,
                userId: data.userId,
            }

            await matchMaking(Tabledata, client);

            return;
        }

    } catch (error) {
        console.log('[SIGN_UP] Error >> ', error);
        Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
            sentId: client.id,
            data: {
                error: true,
                message: `${error}`
            },
            message: `Signup Function Error !`,
        });

    } finally {
        await Lock.getLock().release(MatchMakingLock);

    }

} 