import { Socket } from "socket.io";
import { CONSTANTS } from "../constants";
import { Lock } from "../connection/redlock";
import logger from "../logger";
import { Emitter } from "../playing/events/eventEmmiter";
import { getPlayer, updatePlayer } from "../playing/database/player";


export const winningAck = async (
    data: {},
    client: Socket,
): Promise<void> => {

    const userId = client.handshake.auth.id;

    const redlockId = `${CONSTANTS.REDLOCK.LOCK}:${CONSTANTS.REDIS.PLAYER}:${userId}`;
    const winningAckLock = await Lock.getLock().acquire([redlockId], 2000);

    try {

        if (!userId) {
            logger.info(`Error : ${CONSTANTS.ERROR.WINNING_ACK_VALIDATION}`, { userId });
            throw new Error(CONSTANTS.ERROR.WINNING_ACK_VALIDATION);
        }

        let player = await getPlayer(userId);
        if (!player) { throw new Error('Get Player Data Not Found !'); }

        player.tableId = "";

        await updatePlayer(player);

    } catch (error: any) {
        console.log("winningAck Error : ", error);
        Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
            sentId: client.id,
            data: {
                error: true,
                message: `${error}`
            },
            message: `[winningAck] Function Error !`,
        });
    } finally {
        await Lock.getLock().release(winningAckLock);
    }

}