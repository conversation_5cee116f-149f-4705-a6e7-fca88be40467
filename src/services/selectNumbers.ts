import { Socket } from "socket.io";
import { CONSTANTS } from "../constants";
import logger from "../logger";
import { Lock } from "../connection/redlock";
import { REQUEST_SELECT_NUMBER } from "../interface/event";
import { Emitter } from "../playing/events/eventEmmiter";
import { getPlayerGamePlay, updatePlayerGamePlay } from "../playing/database/pgp";
import { selectNumberInputValidator } from "../playing/validations";
import { getTableGamePlay } from "../playing/database/table";


export const selectNumbers = async (
    data: REQUEST_SELECT_NUMBER,
    client: Socket,
) => {

    const userId = client.handshake.auth?.id;
    const tableId = client.handshake.auth?.tableId;

    const redlockId = `${CONSTANTS.REDLOCK.LOCK}::${tableId}`;
    const selectNumberLock = await Lock.getLock().acquire([redlockId], 2000);

    try {

        const error = await selectNumberInputValidator(data, userId)
        if (error || !userId || !tableId) {
            logger.info(`Error : ${CONSTANTS.ERROR.SELECT_NUMBER_VALIDATION}`, { userId, tableId });
            throw new Error(`${error}`);
        }

        const { tno, sno } = data;
        let validTicket = false;
        let selected = false;

        let getPGP = await getPlayerGamePlay(userId, tableId);
        if (!getPGP) { throw new Error("get PlayerGamePlay Data Failed!"); }

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        if (getTGP.tableState === CONSTANTS.STATE.WIN) { return; }

        if (!getPGP.isAvaliable) {
            getPGP.isAvaliable = true;
            getPGP = await updatePlayerGamePlay(getPGP);
            if (!getPGP) { throw new Error("update PlayerGamePlay Data Failed!"); }
        }

        for (let i = 0; i < getPGP.ticket.length; i++) {
            if (getPGP.ticket[i].ticketNo === tno) {

                validTicket = true;
                const Ticket = getPGP.ticket[i].ticket.flat(Infinity);
                const checkNumber = Ticket.includes(sno);
                if (checkNumber) {

                    const allreadySelectNumber = getPGP.ticket[i].selectNumbers.includes(sno);
                    if (allreadySelectNumber) {

                        if (getTGP.randomNumbers.includes(sno)) {
                            selected = true;
                            // return;
                        }
                        else {

                            selected = false;
                            getPGP.ticket[i].selectNumbers = getPGP.ticket[i].selectNumbers.filter((n) => n !== sno);
                        }

                    } else {
                        selected = true;
                        getPGP.ticket[i].selectNumbers.push(sno);
                    }

                    await updatePlayerGamePlay(getPGP);

                } else {
                    throw new Error(CONSTANTS.ERROR.SELECT_NUMBER_ERROR);
                }
            }
        }

        if (!validTicket) { throw new Error(CONSTANTS.ERROR.WRONG_TICKET_NUMBER); }

        const responseData = { selected };

        Emitter.emit(CONSTANTS.EVENTS.SELECT_NUMBER, {
            sentId: client.id,
            data: responseData,
            message: `TicketNo:${tno} & ${sno} ${CONSTANTS.RESPONSE.NUMBER_SELECT}`,
        });

        return;

    } catch (error) {
        console.log('[select_numbers] Error >> ', error);
        Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
            sentId: client.id,
            data: {
                error: true,
                message: `${error}`
            },
            message: `[selectNumbers] Function Error !`,
        });
    } finally {
        await Lock.getLock().release(selectNumberLock);
    }

} 