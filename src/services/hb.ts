import { Socket } from "socket.io";
import { CONSTANTS } from "../constants";
import { Emitter } from "../playing/events/eventEmmiter";
import logger from "../logger";


export type callbackType = (data: any) => string;

export const heartBeat = async (
    data: {},
    client: Socket,
): Promise<void> => {

    try {

        Emitter.emit(CONSTANTS.EVENTS.HB, {
            sentId: client.id,
            data: {},
            message: 'Heartbeat event successful.',
        });

    } catch (error: any) {
        console.log("heartBeat Error : ", error);
    }

}