import { Socket } from "socket.io";
import { Emitter } from "../playing/events/eventEmmiter";
import { CONSTANTS } from "../constants";
import { Lock } from "../connection/redlock";
import logger from "../logger";
import { getTableGamePlay, updateTableGamePlay } from "../playing/database/table";
import { getPlayerGamePlay, updatePlayerGamePlay } from "../playing/database/pgp";
import { removeData } from "../playing/controller/removeData/removeDbData";
import { getPlayer, updatePlayer } from "../playing/database/player";
import { getOnlineUser, removeOnlineUser, removeOnlineUserLobbyWise } from "../playing/database/onlineStatus";
import { checkWinner } from "../playing/controller/winner/checkWinner";
import { markCompletedGameStatus } from "../playing/clientSideApi";


const leaveTable = async (
    data: any,
    socket: Socket
) => {

    const userId = data.apiUserId ? data.apiUserId : socket.handshake.auth?.id;
    const tableId = data.apiTableId ? data.apiTableId : socket.handshake.auth?.tableId;

    const key = `${CONSTANTS.REDLOCK.LOCK}:${CONSTANTS.REDLOCK.LEAVE_GAME}:${tableId}`;
    const leaveLock = await Lock.getLock().acquire([key], 2000);

    try {

        if (!userId || !tableId) {
            logger.info(`Error : ${CONSTANTS.ERROR.LEAVE_VALIDATION}`, { userId, tableId });
            throw new Error(CONSTANTS.ERROR.LEAVE_VALIDATION);
        }

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { return; }

        let getPGP = await getPlayerGamePlay(userId, tableId);
        if (!getPGP) { throw new Error("Get PlayerGamePlay Data Failed!"); }

        let player = await getPlayer(userId);
        if (!player) { throw new Error("Get Player Data Failed !"); }

        if (getTGP.lock && (getPGP.playerState === CONSTANTS.STATE.DISCONNECT || getTGP.tableState === CONSTANTS.STATE.WAITING_FOR_PLAYER)) {
            logger.info("leaveTable Error : ", { TableLock: getTGP.lock, PlayerState: getPGP.playerState });
            return;
        }

        if (getTGP.lock && getTGP.tableState === CONSTANTS.STATE.WIN) {

            Emitter.emit(CONSTANTS.EVENTS.LEAVE_TABLE, {
                sentId: socket.id,
                data: { leave: true },
                message: `[LEAVE_TABLE] userId : ${userId}`,
            });

        } else {

            logger.info("PlayerList : ", getTGP.playerList);

            let activePlayers = 0;

            for (let i = 0; i < getTGP.playerList.length; i++) {
                const userIds = getTGP.playerList[i].userId;

                let getPGP = await getPlayerGamePlay(userIds, tableId);
                if (!getPGP) { throw new Error("Get PlayerGamePlay Data Failed !"); }

                if (!getPGP.isLeave && !getPGP.isDisconnect) { activePlayers += 1; }

            }
            if (activePlayers === 1 || activePlayers > 1) {

                for (let i = 0; i < getTGP.playerList.length; i++) {
                    if (getTGP.playerList[i].userId === userId) {
                        player.tableId = "";
                        getTGP.playerList[i].leave = true;
                    }
                }

                player = await updatePlayer(player);
                if (!player) { throw new Error("Get Player Data Failed !"); }

                getTGP = await updateTableGamePlay(getTGP);
                if (!getTGP) { throw new Error("Update TableGamePlay Data Failed !"); }

                getPGP.isLeave = true;
                getPGP.playerState = CONSTANTS.STATE.LEAVE;

                getPGP = await updatePlayerGamePlay(getPGP);
                if (!getPGP) { throw new Error("Update PlayerGamePlay Data Failed !"); }

                const activePlayersScore = getTGP.scoreList.filter((player) => player.userId !== userId);
                if (activePlayersScore) { getTGP.scoreList = activePlayersScore; }

                logger.info("leaveTable [activePlayers length] : ", activePlayers);

                // ! Game Is AllMost Complete API Call
                await markCompletedGameStatus(
                    {
                        tableId: tableId,
                        gameId: player.gameId,
                        tournamentId: player.lobbyId,
                    },
                    player.token,
                    player.socketId,
                    player.projectType
                );

                if (activePlayers === 1) {
                    Emitter.emit(CONSTANTS.EVENTS.LEAVE_TABLE, {
                        sentId: socket.id,
                        data: { leave: true },
                        message: `[LEAVE_TABLE] userId : ${userId}`,
                    });

                    // TODO : WINING CALCULATION
                    await checkWinner(tableId, userId, true);

                    await removeData(userId, tableId);
                    
                } else {
                    Emitter.emit(CONSTANTS.EVENTS.LEAVE_TABLE, {
                        sentId: socket.id,
                        data: { leave: true },
                        message: `[LEAVE_TABLE] userId : ${userId}`,
                    });

                    const onlineUserData = await getOnlineUser(userId);
                    if (onlineUserData) {
                        await removeOnlineUser(userId);
                        await removeOnlineUserLobbyWise(userId, onlineUserData.lobbyId);
                    }

                    // await removeDataPlayer(userId, tableId);
                }

            } else {
                logger.info("LEAVE_ACTIVE_PLAYER_ERROR", "")
                throw new Error(CONSTANTS.ERROR.LEAVE_ACTIVE_PLAYER_ERROR);
            }

        }

        socket.handshake.auth = {};
        socket.leave(tableId);
        return;

    } catch (error) {
        console.log('[LeaveTable] Error >> ', error);
        Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
            sentId: socket.id,
            data: {
                error: true,
                message: `${error}`
            },
            message: `leaveTable Function Error !`,
        });

    } finally {
        await Lock.getLock().release(leaveLock);
    }

}

export { leaveTable };