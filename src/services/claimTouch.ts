import { Socket } from "socket.io";
import { Lock } from "../connection/redlock";
import { CONSTANTS } from "../constants";
import { Emitter } from "../playing/events/eventEmmiter";
import { REQUEST_CLAIMED_PATTERN } from "../interface/event";
import { findClaimedPattern } from "../playing/helper/findClaimed";
import { claimedPatternInputValidator } from "../playing/validations";
import logger from "../logger";
import { getPlayerGamePlay } from "../playing/database/pgp";


const claimedPattern = async (
    data: REQUEST_CLAIMED_PATTERN,
    socket: Socket
) => {

    const userId = socket.handshake.auth?.id;
    const tableId = socket.handshake.auth?.tableId;

    const key = `${CONSTANTS.REDLOCK.LOCK}:${CONSTANTS.REDLOCK.PATTERN}:${tableId}`;
    const leaveLock = await Lock.getLock().acquire([key], 2000);

    try {

        const error = await claimedPatternInputValidator(data, userId, tableId);
        if (error) {
            logger.info(`Error : ${CONSTANTS.ERROR.CLAIM_TOUCH_VALIDATION}`, "");
            throw new Error(`${error}`);
        }

        const { ticketNumber } = data;

        let getPGP = await getPlayerGamePlay(userId,tableId);
        if (!getPGP) { throw new Error("get PlayerGamePlay Data Failed!"); }

        const lostTicket = getPGP.lostList.some(n => n.ticketNo === ticketNumber);
        if (lostTicket) {
            logger.info("[claimedPattern] lostTicket !", "")
            return;
        }

        const responseData = await findClaimedPattern(ticketNumber, tableId, userId, true);
        if (!responseData) { throw new Error("[claimedPattern] find Claimed Pattern Failed!"); }

        Emitter.emit(CONSTANTS.EVENTS.PATTERN, {
            sentId: socket.id,
            data: responseData,
            message: CONSTANTS.RESPONSE.CLAIM_PATTERN_SUCCESS,
        });


    } catch (error) {
        console.log('[claimedPattern] Error >> ', error);
        Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
            sentId: socket.id,
            data: {
                error: true,
                message: `${error}`
            },
            message: `claimedPattern Function Error !`,
        });

    } finally {
        await Lock.getLock().release(leaveLock);
    }

}

export { claimedPattern };