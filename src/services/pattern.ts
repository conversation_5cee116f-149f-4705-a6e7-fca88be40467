import { Socket } from "socket.io";
import { Lock } from "../connection/redlock";
import { CONSTANTS } from "../constants";
import { REQUEST_TICKET_PATTERN } from "../interface/event";
import { bottomLine } from "../playing/controller/pattern/bottomLine";
import { corners } from "../playing/controller/pattern/corners";
import { earlyFive } from "../playing/controller/pattern/earlyFive";
import { fullHouse } from "../playing/controller/pattern/fullHouse";
import { middleLine } from "../playing/controller/pattern/middleLine";
import { topLine } from "../playing/controller/pattern/topLine";
import { Emitter } from "../playing/events/eventEmmiter";
import { ticketPatternInputValidator } from "../playing/validations";
import logger from "../logger";
import { checkWinner } from "../playing/controller/winner/checkWinner";
import { getTableGamePlay } from "../playing/database/table";
import { getPlayerGamePlay } from "../playing/database/pgp";


const allTicketPattern = async (
    data: REQUEST_TICKET_PATTERN,
    socket: Socket
) => {

    const userId = socket.handshake.auth?.id;
    const tableId = socket.handshake.auth?.tableId;

    const key = `${CONSTANTS.REDLOCK.LOCK}:${CONSTANTS.REDLOCK.PATTERN}:${tableId}`;
    const leaveLock = await Lock.getLock().acquire([key], 2000);

    try {

        const error = await ticketPatternInputValidator(data, userId, tableId);
        if (error) {
            logger.info(`Error : ${CONSTANTS.ERROR.TIKET_PATTERN_VALIDATION}`, "");
            throw new Error(`${error}`);
        }

        const { type, ticketNumber } = data;

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        if (getTGP.tableState === CONSTANTS.STATE.WIN && getTGP.lock) {
            logger.info("Game Is Allready Winning State So Return !", "");
            return;
        }

        let getPGP = await getPlayerGamePlay(userId, tableId);
        if (!getPGP) { throw new Error("get PlayerGamePlay Data Failed!"); }

        if (getPGP.isLost) {
            // getPGP.lostList.includes(ticketNumber);

            logger.info("Player Lost any one Tickets, S please check [ticketNumber] : ", ticketNumber);

            let count = 0;

            for (let i = 0; i < getPGP.lostList.length; i++) {

                if (ticketNumber === getPGP.lostList[i].ticketNo) { count += 1; }

            }

            if (count > 0) {
                logger.info("[ticketNumber] Found in Lost Ticket , So return Request : ", ticketNumber)
                return;
            }

        }

        const hasAvailablecounts = getTGP.score.some((s) => s.count > 0);
        if (!hasAvailablecounts) {
            logger.info("Calling Winning Functions , NO AVAILABLE COUNTS !!","");
            await checkWinner(tableId, userId, false);
        }

        switch (type) {
            case CONSTANTS.EVENTS.EARLY_FIVE:
                return await earlyFive(data, socket);
            case CONSTANTS.EVENTS.TOP_LINE:
                return await topLine(data, socket);
            case CONSTANTS.EVENTS.MIDDLE_LINE:
                return await middleLine(data, socket);
            case CONSTANTS.EVENTS.BOTTOM_LINE:
                return await bottomLine(data, socket);
            case CONSTANTS.EVENTS.CORNERS:
                return await corners(data, socket);
            case CONSTANTS.EVENTS.FULL_HOUSE:
                return await fullHouse(data, socket);

            default:
                throw new Error("Invalid Request Type !!");
        }

    } catch (error) {
        logger.info('[allTicketPattern] Error >> ', error);
        Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
            sentId: socket.id,
            data: {
                error: true,
                message: `${error}`
            },
            message: `allTicketPattern Function Error !`,
        });

    } finally {
        await Lock.getLock().release(leaveLock);
    }

}

export { allTicketPattern };