export interface REDIS {
    REDIS_HOST: string,
    REDIS_PORT: number,
    REDIS_PASSWORD: string,
    REDIS_DATABASE_NUMBER: number,
    PUBSUB_REDIS_HOST: string,
    PUBSUB_REDIS_PORT: number,
    PUBSUB_REDIS_PASSWORD: string
}

export interface SERVER_CONFIG {
    SERVER_PORT: number,
    CERTIFICATE_CERT_PATH: string,
    CERTIFICATE_KEY_PATH: string,
}

export interface PLATFORM {
    APP_KEY: string,
    APP_DATA: string,
    SECRET_KEY: string,
    GAME_ID: string,
    API_BASE_URL_MGP: string,
    API_BASE_URL_VM: string,
}

export interface LOGGER {
    LOG: boolean,
}

export interface GAMEPLAY {
    PLATFORM_RAKE: number,
    RANGE_OF_RADIUS: number,
    MIN_PLAYER: number,
    MAX_PLAYER: number,
    MIN_TICKET: number,
    MAX_TICKET: number,
    TOTAL_TICKET: number,
    MATCH_MAKING_TIMER: number,
    FULL_HOUSE_PERCENTAGE: number,
    TOP_LINE_PERCENTAGE: number,
    MIDDLE_LINE_PERCENTAGE: number,
    BOTTOM_LINE_PERCENTAGE: number,
    EARLY_FIVE_PERCENTAGE: number,
    CORNERS_PERCENTAGE: number,
    FULL_HOUSE_PERSON: number,
    TOP_LINE_PERSON: number,
    MIDDLE_LINE_PERSON: number,
    BOTTOM_LINE_PERSON: number,
    EARLY_FIVE_PERSON: number,
    CORNERS_PERSON: number,
    GAME_START_TIMER: number,
    DISCONNECT_TIMER: number,
    RANDOM_BALL_TIMER: number,
    GAME_START_DELAY_TIMER: number,
    WINNING_DELAY_TIMER: number,
}

export interface configReturn {
    ENVIRONMENT: string,
    REDIS: REDIS,
    LOGGER: LOGGER,
    SERVER_CONFIG: SERVER_CONFIG,
    PLATFORM: PLATFORM,
    GAMEPLAY: GAMEPLAY,
}

export interface projectTypeInterface {
    VERIFY_USER_PROFILE: string,
    GET_USER_OWN_PROFILE: string,
    DEDUCT_USER_ENTRY_FEE: string,
    MULTI_PLAYER_SUBMIT_SCORE: string,
    GAME_SETTING_MENU_HELP: string,
    MARK_COMPLETED_GAME_STATUS: string,
    GET_ONE_ROBOT: string,
    CHECK_BALANCE?: string,
    REDIUS_CHECK: string,
    FTUE_UPDATE: string,
    CHECK_USER_BLOCK_STATUS: string,
    CHECK_MAINTANENCE: string,
    ADD_GAME_RUNNING_STATUS: string,
    MULTI_PLAYER_DEDUCT_ENTRY_FEE: string,
}