export interface I_MATCH_MAKING {
    entryFee: number;
    ticket: number;
    userId: string;
}

export interface I_PLAYERLIST {
    userId: string;
    userName: string;
    userProfile: string;
    leave: boolean,
}

export interface I_SCORELIST {
    userId: string,
    userName: string,
    userProfile: string,
    score: number,
    price: number,
    type: T_TICKET_TYPE
}

export interface I_RADIUS {
    latitude: string;
    longitude: string;
}

export interface I_COLLECTENTRYFEE {
    userIds: string[],
    tableId: string,
}

export interface I_GAME_START {
    userId: string,
    tableId: string,
}

export interface BULL_MATCH_MAKING {
    userId: string,
    tableId: string,
}

export type T_PLAYER_STATE = 'INITIAL' | 'PLAYING' | 'LEAVE' | 'DISCONNECT';
export type T_TABLE_STATE = 'WAITING_FOR_PLAYER' | 'GAME_START_TIMER' | 'PLAY' | 'WIN';
export type T_TICKET_TYPE = 'EARLY_FIVE' | 'TOP_LINE' | 'MIDDLE_LINE' | 'BOTTOM_LINE' | 'CORNERS' | 'FULL_HOUSE';

export interface I_SCORE {
    score: number,
    price: number,
    count: number,
    type: T_TICKET_TYPE,
    claimed: boolean,
}

export interface I_TICKETS {
    ticketNo: number,
    ticket: number[][],
    selectNumbers: number[],
}

export interface I_REJOIN {
    userId: string,
    tableId: string,
}

export interface I_DISCONNECT_TIMER {
    userId: string,
    tableId: string,
}

export interface I_RANDOM_BALL_TIMER {
    userId: string,
    tableId: string,
}

export interface I_CLAIMED {
    ticketNumber: number,
    type: T_TICKET_TYPE,
}

export interface I_COLOR_BALL_TIMER {
    color: string;
    ballNumber: number;
    timer: number;
}

export interface SELF_I {
    userId: string;
    userName: string;
    userProfile: string;
    score: number;
    result: string;
};

export interface I_PATTERN_RESPONSE {
    [x: string]: any;
    pattern: I_SCORE[];
    claims: number;
    isClaimTouch: boolean;
}

export interface I_CLAIM_COUNT_RESPONSE {
    pattern: {
        count: number;
        type: T_TICKET_TYPE;
    }[];
    claim: number,
}

export interface I_WINNING_TIMER {
    userId: string,
    tableId: string,
    isDisconectOrLeave: boolean,
}

export interface I_INVALID_TICKET {
    ticketNo: number,
    message: string
}

export interface I_ALL_WINNER {
    userId: string,
    userName: string,
    userProfile: string,
    score: number,
    won: number,
    type: T_TICKET_TYPE,
    you: boolean,
}

export interface I_WINNING_SCREEN {
    // self: {
    //     userId: string;
    //     userName: string;
    //     userProfile: string;
    //     score: number;
    //     won: number;
    //     result: string;
    // };
    winner: I_ALL_WINNER[];
}

export interface I_USERLISTS {
    userId: string,
    userName: string,
    userProfile: string,
    score: number,
    price: number,
}
