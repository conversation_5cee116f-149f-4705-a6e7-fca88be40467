import { T_TICKET_TYPE } from "./common";

export interface RESPONSE_EMMITER {
    sentId: string,
    data: object,
    message: string,
    error?: boolean
}

export interface REQUEST_SIGNUP {
    userId: string,
    userName: string,
    userProfile: string,
    entryFee: number,
    ticket: number | string,

    token: string,
    lobbyId: string;
    gameId: string;
    isFTUE: boolean;
    latitude?: string;
    longitude?: string;
    projectType: string;
    gameModeId: string;
}

export interface RESPONSE_JOINTABLE {
    total_Time: number,
    remaining_Time: number,
    total_Player: number;
    tickets_Cost: number;
    total_Tickets: number;
    top_Line: number;
    middle_line: number;
    bottom_Line: number;
    early_Five: number;
    corners: number;
    full_House: number;
}

export interface REQUEST_SELECT_NUMBER {
    tno: number;
    sno: number;
}

export interface REQUEST_TICKET_PATTERN {
    ticketNumber: number;
    type: T_TICKET_TYPE;
}

export interface REQUEST_CLAIMED_PATTERN {
    ticketNumber: number;
}