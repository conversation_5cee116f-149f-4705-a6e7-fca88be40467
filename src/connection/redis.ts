import { createClient } from "redis";
import { getconfig } from "../config";
import IORedis, { Redis as IORedisClient } from 'ioredis';
import { CONSTANTS } from "../constants";

export type RedisClientType = ReturnType<typeof createClient>;
export type PubClientType = IORedisClient;
export type SubClientType = IORedisClient;

let redisClient: RedisClientType;
let pubClient: PubClientType;
let subClient: SubClientType;

const redisConnection = async (): Promise<void> => {

    try {

        const {
            REDIS_HOST,
            REDIS_PORT,
            REDIS_PASSWORD,
            REDIS_DATABASE_NUMBER,
            PUBSUB_REDIS_HOST,
            PUBSUB_REDIS_PORT,
            PUBSUB_REDIS_PASSWORD
        } = getconfig().REDIS;

        redisClient = createClient({
            socket: {
                host: REDIS_HOST,
                port: Number(REDIS_PORT),
            },
            password: REDIS_PASSWORD,
            database: Number(REDIS_DATABASE_NUMBER),
        });

        const PubSubRedisOptions: any = {

            host: PUBSUB_REDIS_HOST,
            port: Number(PUBSUB_REDIS_PORT),
            password: PUBSUB_REDIS_PASSWORD,
            db: Number(REDIS_DATABASE_NUMBER)

        }

        pubClient = new IORedis(PubSubRedisOptions);
        subClient = new IORedis(PubSubRedisOptions);

        redisClient.connect();

        redisClient.on('error', (error: Error) => {
            console.log('[Redis] >> Client Error >>', error);
        });

        redisClient.on('connect', () => {
            console.log('[Redis] >>', 'Connected successfully.');

            if ((process.env.ENVIRONMENT)?.toUpperCase() === CONSTANTS.ENVIRONMENT.LOCAL) {
                redisClient.flushDb();
            }

        });

        pubClient.on('error', (error: Error) => {
            console.log('[PUBSUB] >> Client Error >>', error);
        });

        pubClient.on('ready', () => {
            console.log('[PUBSUB] >>', 'Connected successfully.');
        });

    } catch (error: any) {
        console.log('[redisConnection] Catch Error :: ', error);
        return error;
    }
}

export { redisConnection, redisClient, pubClient, subClient };