import Redis from 'ioredis';
import Redlock from 'redlock';
import { getconfig } from '../config';
import logger from '../logger';

// let redlock: Redlock | undefined;

// const registerRedlockError = (redlock: Redlock): void => {
//     redlock.on('error', (error: Error) => {
//         console.error('REDIS_LOCK_ERROR', error)
//     });
// }

// const initializeRedlock = (): Redlock => {
//     if (redlock) return redlock;

//     const { REDIS } = getconfig();
//     console.log(REDIS.REDIS_HOST, REDIS.REDIS_PORT, REDIS.REDIS_DATABASE_NUMBER);


//     if (!REDIS.REDIS_HOST || !REDIS.REDIS_PORT || !REDIS.REDIS_DATABASE_NUMBER) {
//         throw new Error("Env Data Not Found !!");
//     }

//     const gamePlayconfig = {
//         host: REDIS.REDIS_HOST,
//         port: Number(REDIS.REDIS_PORT),
//         family: 4,
//         password: REDIS.REDIS_PASSWORD ? REDIS.REDIS_PASSWORD : '',
//         db: Number(REDIS.REDIS_DATABASE_NUMBER)
//     };

//     const redisClient = new Redis(gamePlayconfig);
//     redlock = new Redlock([redisClient], {
//         driftFactor: 0.01,
//         retryCount: -1,
//         retryDelay: 25,
//         retryJitter: 20,
//     });

//     if (redlock) {
//         registerRedlockError(redlock);
//     } else {
//         throw new Error('Failed to initialize Redlock');
//     }
//     return redlock;
// }

// export const Lock = {
//     init: initializeRedlock,
//     getLock: (): Redlock => {
//         if (!redlock) {
//             throw new Error('Redlock is not initialized. Call init() before getLock().');
//         }
//         return redlock;
//     },
// }


let redlock: any;

const registerRedlockError = (): void => {
    redlock.on('error', (error: Error) => {
        console.log('REDIS_LOCK_ERROR : ', error)
    });
}

const initializeRedlock = (): void => {
    if (redlock) return redlock;
    const {
        REDIS_HOST,
        REDIS_PORT,
        REDIS_PASSWORD,
        REDIS_DATABASE_NUMBER
    } = getconfig().REDIS;

    const gamePlayconfig = {
        host: REDIS_HOST,
        port: REDIS_PORT,
        family: 4,
        password: REDIS_PASSWORD ? REDIS_PASSWORD : '',
        db: REDIS_DATABASE_NUMBER
    };

    const redisClient = new Redis(gamePlayconfig);
    redlock = new Redlock([redisClient], {
        driftFactor: 0.01,
        retryCount: -1,
        retryDelay: 25,
        retryJitter: 20,
    });

    registerRedlockError();
    return redlock;
}

export const Lock = {
    init: initializeRedlock,
    getLock: (): Redlock => redlock,
}