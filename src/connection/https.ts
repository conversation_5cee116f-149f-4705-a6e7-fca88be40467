import express from 'express';
import 'dotenv/config';
import { existsSync, readFileSync } from 'fs';
import https, { createServer } from 'https';
import http from 'http';
import cors from 'cors';
import bodyParser from 'body-parser';
import { router } from '../routes';
import { getconfig } from '../config';
import logger from '../logger';

const app = express();
let httpsServer: http.Server | https.Server;

const setupExpressApp = async () => {

    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));
    app.use(bodyParser.urlencoded({ extended: true }));
    app.use(bodyParser.json({ limit: '50mb' }));
    app.use(cors());
    app.use(router);

}

const startServer = async (server: http.Server | https.Server, port: number) => {

    server.listen(port, () => {
        const protocol = server instanceof https.Server ? 'HTTPS' : 'HTTP';
        console.log(`[${protocol}] >> Server listening on port: ${port}`);
    });

}

const httpsConnection = async () => {
    const { SERVER_CONFIG } = getconfig();

    logger.info("Config : ", getconfig());

    if (SERVER_CONFIG.CERTIFICATE_KEY_PATH && SERVER_CONFIG.CERTIFICATE_CERT_PATH) {
        const KeyPath = SERVER_CONFIG.CERTIFICATE_KEY_PATH;
        const CertPath = SERVER_CONFIG.CERTIFICATE_CERT_PATH;

        if (existsSync(KeyPath) && existsSync(CertPath)) {
            const KeyData = readFileSync(KeyPath, 'utf-8');
            const CertData = readFileSync(CertPath, 'utf-8');
            const ServerOptions = { key: KeyData, cert: CertData }
            httpsServer = createServer(ServerOptions, app);
        } else {
            httpsServer = http.createServer(app);
        }
    } else {
        httpsServer = http.createServer(app);
    }

    await setupExpressApp();
    await startServer(httpsServer, SERVER_CONFIG.SERVER_PORT);
}

export { httpsServer, httpsConnection };
