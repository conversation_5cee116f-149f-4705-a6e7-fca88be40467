import { DisconnectReason, Server, ServerOptions, Socket } from 'socket.io';
import { requestHandler } from '../requestHandler/request';
import { httpsServer } from './https';
import { disconnectHandler } from '../playing/disconnect/disconnect';
import { pubClient, subClient } from './redis';
import { createAdapter } from '@socket.io/redis-adapter';
import logger from '../logger';


let io: Server;

const socketConnection = async (): Promise<void> => {

    const server = httpsServer;

    const socketConfig: Partial<ServerOptions> = {
        transports: ["polling", "websocket"],
        allowUpgrades: true,
        maxHttpBufferSize: 1e8,
        pingInterval: 5000, // 2000
        pingTimeout: 5000, // 10000
        connectTimeout: 10000, // 10000
    }

    io = new Server(server, socketConfig);

    io.adapter(createAdapter(pubClient, subClient));

    io.on('connection', async (socket: Socket) => {

        logger.info('New Socket Connected >>', socket.id);

        await requestHandler(socket);

        socket.on('disconnect', async (reason: DisconnectReason) => {
            logger.info(`Socket [${socket.id}] Disconnected Reson >> ${reason}`, "");
            await disconnectHandler(socket);
        })

    });

}

export { socketConnection, io };