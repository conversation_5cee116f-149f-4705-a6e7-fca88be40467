import { Socket } from "socket.io";
import { leaveTable } from "../services/leave";
import { signup } from "../services/signup";
import { CONSTANTS } from "../constants";
import { heartBeat } from "../services/hb";
import { selectNumbers } from "../services/selectNumbers";
import { convertStringtoObject } from "../playing/helper";
import logger from "../logger";
import { Emitter } from "../playing/events/eventEmmiter";
import { allTicketPattern } from "../services/pattern";
import { playerList } from "../services/playerList";
import { winningAck } from "../services/winningAck";
import { claimedPattern } from "../services/claimTouch";

const requestHandler = async (socket: Socket): Promise<void> => {

    try {

        socket.onAny(async (listener, data) => {

            try {

                if (data) { data = convertStringtoObject(data); }
                const value = data.data;
                const userId = socket.handshake.auth.id;
                const tableId = socket.handshake.auth.tableId;

                if (listener !== CONSTANTS.EVENTS.HB) {
                    logger.info(`[Request Frontend] [${listener}] : `, { userId, tableId, value });
                }

                switch (listener) {
                    case CONSTANTS.EVENTS.HB:
                        return await heartBeat(value, socket);
                    case CONSTANTS.EVENTS.SIGN_UP:
                        return await signup(value, socket);
                    case CONSTANTS.EVENTS.LEAVE_TABLE:
                        return await leaveTable(value, socket);
                    case CONSTANTS.EVENTS.SELECT_NUMBER:
                        return await selectNumbers(value, socket);
                    case CONSTANTS.EVENTS.PLAYERLIST:
                        return await playerList(value, socket);
                    case CONSTANTS.EVENTS.NUMBER_LIST:
                        return; // await selectedNumberList(value, socket);
                    case CONSTANTS.EVENTS.PATTERN:
                        return await allTicketPattern(value, socket);
                    case CONSTANTS.EVENTS.WINNING_ACK:
                        return await winningAck(value, socket);
                    case CONSTANTS.EVENTS.CLAIM_TOUCH:
                        return await claimedPattern(value, socket);

                    default:
                        throw new Error(CONSTANTS.ERROR.REQUEST_SWITCHCASE_ERROR);
                }

            } catch (error) {
                console.log('[requestHandler] Event Handler Error :: ', error);
                Emitter.emit(CONSTANTS.EVENTS.ERROR_POPUP, {
                    sentId: socket.id,
                    data: {
                        error: true,
                        message: `${listener} : Request Not Valid !!`
                    },
                    message: CONSTANTS.ERROR.REQUEST_HANDLER_VALIDATION,
                });
            }

        })

    } catch (error: any) {
        console.log('[requestHandler] Catch Error :: ', error);
    }

}

export { requestHandler };

