import { <PERSON><PERSON><PERSON><PERSON>, Job } from "bull";
import { I_WINNING_SCREEN, I_WINNING_TIMER } from "../../interface/common";
import { getTableGamePlay } from "../../playing/database/table";
import { getPlayerGamePlay } from "../../playing/database/pgp";
import logger from "../../logger";
import { getPlayer } from "../../playing/database/player";
import { winnerResponsefinder } from "../../playing/helper/scoreFinder";
import { CONSTANTS } from "../../constants";
import { Emitter } from "../../playing/events/eventEmmiter";
import { removeData } from "../../playing/controller/removeData/removeDbData";
import { markCompletedGameStatus, multiPlayerWinnScore } from "../../playing/clientSideApi";


export const delayWinningProcess = async (job: Job<I_WINNING_TIMER>, done: DoneCallback) => {

    try {

        logger.info("In delayWinningProcess : ", { JobData: job.data });

        done();

        const { tableId, userId } = job.data;
        if (tableId && userId) {

            let getTGP = await getTableGamePlay(tableId);
            if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

            let getPGP = await getPlayerGamePlay(userId, tableId);
            if (!getPGP) { throw new Error("Get PlayerGamePlay Data Failed!"); }

            const WinnerResponse = await winnerResponsefinder(tableId, false);
            if (!WinnerResponse) { throw new Error("Winner Response finder Failed!"); }

            logger.info("Winning Response : ", WinnerResponse);

            const ApiWinnerResponse = WinnerResponse.map((winnerItem, index) => ({
                userId: winnerItem.userId,
                score: `${winnerItem.score}`,
                rank: `${index + 1}`,
                winningAmount: winnerItem.won,
                winLossStatus: winnerItem.won > 0 ? "Win" : "Loss",
            }));

            logger.info("ApiWinnerResponse : ", ApiWinnerResponse)

            if (ApiWinnerResponse && ApiWinnerResponse.length > 1) {

                let player = await getPlayer(ApiWinnerResponse[0].userId);
                if (!player) { throw new Error("Get Player Data Failed !"); }

                let apiPayload = {
                    tableId: tableId,
                    tournamentId: player.lobbyId,
                    playersScore: ApiWinnerResponse,
                };

                // ! for multi player score submit api
                const winmultiPlayerWinnScore = await multiPlayerWinnScore(
                    apiPayload,
                    player.token,
                    player.socketId,
                    player.projectType
                );

                logger.info("multiPlayerWinnScore response  >>>> : ", winmultiPlayerWinnScore);

            }

            for (let i = 0; i < getTGP.playerList.length; i++) {
                const userIds = getTGP.playerList[i].userId;

                let getPGP = await getPlayerGamePlay(userIds, tableId);
                if (!getPGP) { throw new Error("Get PlayerGamePlay Data Failed!"); }

                if (!getTGP.playerList[i].leave && !getPGP.isLeave && !getPGP.isDisconnect) {
                    let player = await getPlayer(userIds);
                    if (!player) { throw new Error("Get Player Data Failed!"); }

                    // ! Game Is AllMost Complete API Call
                    await markCompletedGameStatus(
                        {
                            tableId: tableId,
                            gameId: player.gameId,
                            tournamentId: player.lobbyId,
                        },
                        player.token,
                        player.socketId,
                        player.projectType
                    );

                    const updatedWinner = WinnerResponse.map((user) => {
                        if (user.userId === userIds) {
                            return { ...user, you: true };
                        }
                        return user;
                    });

                    // const scoreFInder = await findScore(userIds, tableId);
                    // const moneyFInder = await findMoney(userIds, tableId);
                    const responseData: I_WINNING_SCREEN = {
                        // self: {
                        //     userId: WinnerResponse[0].userId,
                        //     userName: WinnerResponse[0].userName,
                        //     userProfile: WinnerResponse[0].userProfile,
                        //     score: WinnerResponse[0].score,
                        //     won: WinnerResponse[0].won,
                        //     result: WinnerResponse[0].won !== 0 ? CONSTANTS.GAME.WINNER : CONSTANTS.GAME.LOOSER,
                        // },
                        winner: updatedWinner,
                    }

                    Emitter.emit(CONSTANTS.EVENTS.WINNING, {
                        sentId: player.socketId,
                        data: responseData,
                        message: CONSTANTS.RESPONSE.WINNING_SUCCESS,
                    });
                }

            }

            await removeData(userId, tableId);

            return Promise.resolve("Done");

        } else {
            return Promise.reject("Invalid Request Data [delayWinningProcess]");
        }

    } catch (error) {
        logger.info("[delayWinningProcess] Error: ", error);
        return Promise.reject(error);
    }
}
