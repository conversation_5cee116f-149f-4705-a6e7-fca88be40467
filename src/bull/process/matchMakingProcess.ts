import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "bull";
import { BULL_MATCH_MAKING, I_COLLECTENTRYFEE, I_GAME_START } from "../../interface/common";
import logger from "../../logger";
import { getTableGamePlay, updateTableGamePlay } from "../../playing/database/table";
import { getconfig } from "../../config";
import { CONSTANTS } from "../../constants";
import { Emitter } from "../../playing/events/eventEmmiter";
import { removeData } from "../../playing/controller/removeData/removeDbData";
import { removeValueFromArray } from "../../playing/database/redis";
import { getPlayer, updatePlayer } from "../../playing/database/player";
import { multiPlayerEntryFeeDeduction } from "../../playing/controller/entryFee/entryfeeDeduct";
import { getPlayerGamePlay } from "../../playing/database/pgp";
import { gameStartQueue } from "../queue/gameStartQueue";
import { genratePlayerList } from "../../playing/helper/genratePlayerList";
import { markCompletedGameStatus } from "../../playing/clientSideApi";

export const matchMakingProcess = async (job: Job<BULL_MATCH_MAKING>, done: DoneCallback) => {

    try {

        logger.info("In matchMakingProcess : ", { JobData: job.data });

        done();

        const { tableId, userId } = job.data;
        const { MIN_PLAYER, GAME_START_TIMER } = getconfig().GAMEPLAY;
        let userIds: string[] = [];

        if (tableId && userId) {

            let getTGP = await getTableGamePlay(tableId);
            if (!getTGP) { throw new Error("MatchMaking Time Not Found TableGamePlay!"); }

            let player = await getPlayer(userId);
            if (!player) { throw new Error("Get Player Data Failed!"); }

            await removeValueFromArray(getTGP.entryFee, tableId, player.ticket);

            if (getTGP.playerList.length >= MIN_PLAYER) {
                logger.info("Game Start for tableId : ", tableId);

                const userLists = await genratePlayerList(tableId, 4, userId);
                if (!userLists) { throw new Error("[matchMakingProcess] playerList Not Found !"); }

                getTGP.lock = true;
                getTGP.waitingTable[0].remaining_Time = 0;
                getTGP = await updateTableGamePlay(getTGP);
                if (!getTGP) { throw new Error("Update TableGamePlay Not Found !!"); }

                for (let i = 0; i < getTGP.playerList.length; i++) {

                    userIds.push(getTGP.playerList[i].userId);

                    let player = await getPlayer(getTGP.playerList[i].userId);
                    let getPGP = await getPlayerGamePlay(getTGP.playerList[i].userId, tableId);
                    if (player && getPGP) {

                        const selectTableResponse = {
                            tableId: getTGP._id,
                            player: {
                                userId: player.userId,
                                userName: player.userName,
                                userProfile: player.userProfile,
                                score: getPGP.tickitScore,
                                price: Number(getPGP.totalclaimedMoney.toFixed(3))
                            },
                            totalTickets: getTGP.total_ticket,
                            claims: getTGP.claims,
                            winningPatterns: getTGP.score,
                            randomNumbers: getTGP.randomNumbers,
                            ticket: getPGP.ticket,
                        };

                        Emitter.emit(CONSTANTS.EVENTS.SELECT_TABLE, {
                            sentId: player.socketId,
                            data: selectTableResponse,
                            message: CONSTANTS.RESPONSE.CREATE_TABLE,
                        });

                    }
                }

                if (userLists) {

                    getTGP.userList = userLists;

                    getTGP = await updateTableGamePlay(getTGP);
                    if (!getTGP) { throw new Error("Update TableGamePlay Not Found !!"); }

                    Emitter.emit(CONSTANTS.EVENTS.USERLIST, {
                        sentId: tableId,
                        data: { userList: userLists, isRejoin: false },
                        message: CONSTANTS.RESPONSE.USERLIST_SUCCESS,
                    });
                }

                let player = await getPlayer(userId);
                if (!player) { throw new Error("Player Not Found!"); }

                const Bull_data: I_COLLECTENTRYFEE = { tableId, userIds };

                const collectEntryFeeStatus = await multiPlayerEntryFeeDeduction(Bull_data);
                if (!collectEntryFeeStatus) {
                    logger.info("Game Closure: Entry Fee Payment Issue Detected", { collectEntryFeeStatus })
                    return Promise.reject("[matchMakingProcess] Game Closure: Entry Fee Payment Issue Detected");
                } else {

                    const bullData: I_GAME_START = { tableId, userId: getTGP.playerList[0].userId };
                    await gameStartQueue(bullData);

                    getTGP.tableState = CONSTANTS.STATE.GAME_START_TIMER;
                    getTGP = await updateTableGamePlay(getTGP);
                    if (!getTGP) { throw new Error("Update TableGamePlay Not Found !!"); }

                    const gameStartResponse = { timer: GAME_START_TIMER };
                    Emitter.emit(CONSTANTS.EVENTS.GAME_START, {
                        sentId: tableId,
                        data: gameStartResponse,
                        message: CONSTANTS.RESPONSE.GAME_START,
                    });
                }

            } else {

                player.tableId = "";

                player = await updatePlayer(player);
                if (!player) { throw new Error("Update Player Data Failed!"); }

                // ! Game Is AllMost Complete API Call
                await markCompletedGameStatus(
                    {
                        tableId: tableId,
                        gameId: player.gameId,
                        tournamentId: player.lobbyId,
                    },
                    player.token,
                    player.socketId,
                    player.projectType
                );

                Emitter.emit(CONSTANTS.EVENTS.NO_PLAYER, {
                    sentId: tableId,
                    data: { message: CONSTANTS.RESPONSE.NO_PLAYER_MESSAGE },
                    message: CONSTANTS.RESPONSE.NO_PLAYER_FOUND,
                });

                await removeData(userId, tableId);

            }

            return Promise.resolve(job.data);

        } else {

            return Promise.reject("Invalid Request Data [matchMakingProcess]");

        }

    } catch (error) {
        logger.info("[matchMakingProcess] Error: ", error);
        return error;
    }
}
