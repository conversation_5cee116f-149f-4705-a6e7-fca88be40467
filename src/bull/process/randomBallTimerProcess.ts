import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "bull";
import { I_RANDOM_BALL_TIMER } from "../../interface/common";
import { getTableGamePlay } from "../../playing/database/table";
import logger from "../../logger";
import { reGenrateBull } from "./gameStartExtendProcess";
import { checkWinner } from "../../playing/controller/winner/checkWinner";
import { CONSTANTS } from "../../constants";
import { Lock } from "../../connection/redlock";
import { getPlayerGamePlay, updatePlayerGamePlay } from "../../playing/database/pgp";
import { Emitter } from "../../playing/events/eventEmmiter";
import { getPlayer } from "../../playing/database/player";


export const randomBullTimerProcess = async (job: Job<I_RANDOM_BALL_TIMER>, done: DoneCallback) => {

    const key = `${CONSTANTS.REDLOCK.LOCK}:${CONSTANTS.REDLOCK.RANDOMBALL}:${job.data.tableId}`;
    const leaveLock = await Lock.getLock().acquire([key], 2000);

    try {

        done();

        const { tableId, userId } = job.data;
        if (tableId && userId) {

            let getTGP = await getTableGamePlay(tableId);
            if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

            const hasAvailablecounts = getTGP.score.some((s) => s.count > 0);
            if (!hasAvailablecounts) {
                logger.info("Calling Winning Functions , NO AVAILABLE COUNTS !!","");
                await checkWinner(tableId, userId, false);
            }

            if (getTGP.randomNumbers.length === 30 || getTGP.randomNumbers.length === 50 || getTGP.randomNumbers.length === 70 || getTGP.randomNumbers.length === 80) {
                logger.info("RandomNumbers Length : ", getTGP.randomNumbers.length);

                for (let i = 0; i < getTGP.playerList.length; i++) {
                    const userIds = getTGP.playerList[i].userId;
                    let getPGP = await getPlayerGamePlay(userIds, tableId);
                    if (getPGP && !getPGP.isAvaliable && !getPGP.isLeave && !getPGP.isDisconnect) {

                        logger.info("Still Avaliable or Not 1 : ", { userIds, isAvaliable: getPGP.isAvaliable });

                        if (getPGP.isLost && getPGP.ticket.length === getPGP.lostList.length) {
                            logger.info("Player Lost All Ticket : ", getPGP.lostList);
                        } else {

                            // event name - alert
                            let player = await getPlayer(userIds);
                            if (player) {

                                Emitter.emit(CONSTANTS.EVENTS.WINORLOSS, {
                                    sentId: player.socketId,
                                    data: { message: `Please select a number from any ticket.` },
                                    message: `RandomNumbers Length: ${getTGP.randomNumbers.length} on TableId : ${tableId}.`,
                                });

                            }

                        }
                    }

                    if (getPGP && getPGP.isAvaliable) {

                        getPGP.isAvaliable = false;
                        await updatePlayerGamePlay(getPGP);
                    }

                }
            }

            logger.info("randomBullTimerProcess : ", { tableId, totalNumbers: getTGP.totalNumbers.length, randomNumbers: getTGP.randomNumbers.length });

            if (getTGP.totalNumbers.length > 0) {

                logger.info("Remaining Count - ", { tableId, totalNumbers_length: getTGP.totalNumbers.length });
                await reGenrateBull(tableId, userId);
            } else {

                logger.info("|| Game Is Over || Remaining Ball Over || ", "");

                // if ((process.env.ENVIRONMENT)?.toUpperCase() !== CONSTANTS.ENVIRONMENT.LOCAL) {

                await checkWinner(tableId, userId, false);
                // }
            }

            return Promise.resolve(job.data);

        } else {
            return Promise.reject("Invalid Request Data [randomBullTimerProcess]");
        }

    } catch (error) {
        logger.info("[randomBullTimerProcess] Error: ", error);
        return Promise.reject(error);

    } finally {
        await Lock.getLock().release(leaveLock);
    }
}