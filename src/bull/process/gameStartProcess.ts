import Bull, { <PERSON><PERSON><PERSON><PERSON>, Job } from "bull";
import { I_GAME_START } from "../../interface/common";
import logger from "../../logger";
import { CONSTANTS } from "../../constants";
import { gameStartTimer } from "../allJobs";
import { gameStartExtendQueue } from "../queue/gameStartExtendQueue";


export const gameStartProcess = async (job: Job<I_GAME_START>, done: DoneCallback) => {

    try {

        done();

        logger.info("temp - In gameStartProcess ", "");

        const { tableId, userId } = job.data;
        if (tableId && userId) {

            await gameStartExtendTimerBull(tableId, userId);

            return Promise.resolve(job.data);

        } else {
            return Promise.reject("Invalid Request Data [gameStartProcess]");
        }

    } catch (error) {
        logger.info("[gameStartProcess] Error: ", error);
        return Promise.reject(error);
    }
}


export const gameStartExtendTimerBull = async (tableId: string, userId: string): Promise<void> => {

    try {

        logger.info("temp - In gameStartExtendTimerBull !", "");

        const jobKey: Bull.JobId = `${CONSTANTS.BULL.GAME_START_EXTEND_BULL}:${tableId}`;

        const job = await gameStartTimer.getJob(jobKey);
        if (job) { job.remove(); }

        const bullData: I_GAME_START = { tableId, userId }

        await gameStartExtendQueue(bullData);

    } catch (error) {
        logger.info("[gameStartExtendTimerBull] Error : ", error)
    }

}