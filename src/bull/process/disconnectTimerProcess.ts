import { <PERSON><PERSON><PERSON><PERSON>, Job } from "bull";
import { I_DISCONNECT_TIMER } from "../../interface/common";
import { getTableGamePlay, updateTableGamePlay } from "../../playing/database/table";
import { getPlayerGamePlay, updatePlayerGamePlay } from "../../playing/database/pgp";
import { removeData } from "../../playing/controller/removeData/removeDbData";
import { CONSTANTS } from "../../constants";
import logger from "../../logger";
import { getOnlineUser, removeOnlineUser, removeOnlineUserLobbyWise } from "../../playing/database/onlineStatus";
import { checkWinner } from "../../playing/controller/winner/checkWinner";
import { markCompletedGameStatus } from "../../playing/clientSideApi";
import { getPlayer } from "../../playing/database/player";


export const disconnectProcess = async (job: Job<I_DISCONNECT_TIMER>, done: DoneCallback) => {

    try {

        logger.info("In disconnectProcess : ", { JobData: job.data });

        done();

        const { tableId, userId } = job.data;
        if (tableId && userId) {

            let getTGP = await getTableGamePlay(tableId);
            if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

            let getPGP = await getPlayerGamePlay(userId, tableId);
            if (!getPGP) { throw new Error("Get PlayerGamePlay Data Failed!"); }

            if (getPGP.isDisconnect) {
                logger.info("Player Allready Disconnected , So Disconnect Process is Close.", "")
                return;
            }

            const isleavePlayer = getTGP.playerList.find((n) => { return n.userId === userId });
            if (isleavePlayer?.leave) {
                logger.info("Player Allready Leave , So Disconnect Process is Close.", "")
                return;
            }

            getPGP.isDisconnect = true;
            getPGP.playerState = CONSTANTS.STATE.DISCONNECT;

            getPGP = await updatePlayerGamePlay(getPGP);
            if (!getPGP) { throw new Error("Update PlayerGamePlay Data Failed !"); }

            const onlineUserData = await getOnlineUser(userId);
            if (onlineUserData) {
                await removeOnlineUser(userId);
                await removeOnlineUserLobbyWise(userId, onlineUserData.lobbyId);
            }

            // const activePlayers = getTGP.playerList.filter((player) => !player.leave);
            let activePlayers = 0;

            for (let i = 0; i < getTGP.playerList.length; i++) {
                const userIds = getTGP.playerList[i].userId;

                let getPGP = await getPlayerGamePlay(userIds, tableId);
                if (!getPGP) { throw new Error("Get PlayerGamePlay Data Failed !"); }

                if (!getPGP.isLeave && !getPGP.isDisconnect) { activePlayers += 1; }

            }

            logger.info("PlayerList : ", getTGP.playerList);

            let player = await getPlayer(userId);
            if (!player) { throw new Error("Get Player Data Failed!"); }

            // ! Game Is AllMost Complete API Call
            await markCompletedGameStatus(
                {
                    tableId: tableId,
                    gameId: player.gameId,
                    tournamentId: player.lobbyId,
                },
                player.token,
                player.socketId,
                player.projectType
            );

            if (activePlayers === 0) {

                // TODO : WINING CALCULATION
                await checkWinner(tableId, userId, true);

                await removeData(userId, tableId);

            } else if (activePlayers >= 1) {


                // ~ Disconnect Player status not leave , so don't update leave status.
                // for (let i = 0; i < getTGP.playerList.length; i++) {
                //     const id = getTGP.playerList[i].userId;
                //     if (id === userId) {
                //         getTGP.playerList[i].leave = true;
                //     }
                // }

                getTGP = await updateTableGamePlay(getTGP);
                if (!getTGP) { throw new Error("Update TableGamePlay Data Failed !"); }

                // ~ Do not remove PlayerGamePlay Data before winning.
                // await removeDataPlayer(userId, tableId);

            } else {

                throw new Error(CONSTANTS.ERROR.DISCONNECT_ACTIVE_PLAYER_ERROR);
            }

            logger.info("|| GAME END || disconnectProcess || ", "");

            return;

        } else {
            return Promise.reject("Invalid Request Data [disconnectProcess]");
        }

    } catch (error) {
        logger.info("[disconnectProcess] Error: ", error);
        return Promise.reject(error);
    }
}
