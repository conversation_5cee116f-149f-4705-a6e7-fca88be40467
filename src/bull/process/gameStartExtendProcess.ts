import Bull, { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "bull";
import { I_COLOR_BALL_TIMER, I_GAME_START } from "../../interface/common";
import { getTableGamePlay, updateTableGamePlay } from "../../playing/database/table";
import logger from "../../logger";
import { getPlayerGamePlay, updatePlayerGamePlay } from "../../playing/database/pgp";
import { CONSTANTS } from "../../constants";
import { randomNumberShuffle } from "../../playing/helper/numberGenrate";
import { Emitter } from "../../playing/events/eventEmmiter";
import { getconfig } from "../../config";
import { randomBallTimerQueue } from "../queue/randomBallTimerQueue";
import { randomBallTimer } from "../allJobs";
import { getColor } from "../../playing/helper";


export const gameStartExtendProcess = async (job: Job<I_GAME_START>, done: DoneCallback) => {

    try {

        done();

        logger.info("temp - In gameStartExtendProcess ", "");

        const { tableId, userId } = job.data;
        if (tableId && userId) {

            let getTGP = await getTableGamePlay(tableId);
            if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

            // ~ (genrate random number suffle)
            const ballNumberSuffle = await randomNumberShuffle(tableId);
            if (!ballNumberSuffle) { throw new Error(CONSTANTS.ERROR.NUMBER_SHUFFLE_ERROR); }

            getTGP.totalNumbers = ballNumberSuffle;
            getTGP.lock = false;
            getTGP.tableState = CONSTANTS.STATE.PLAY;

            getTGP = await updateTableGamePlay(getTGP);
            if (!getTGP) { throw new Error("Update Table Game Play Data Failed!"); }

            for (let i = 0; i < getTGP.playerList.length; i++) {

                let getPGP = await getPlayerGamePlay(getTGP.playerList[i].userId, tableId);
                if (getPGP && !getTGP.playerList[i].leave && !getPGP.isDisconnect && (getPGP.playerState !== CONSTANTS.STATE.DISCONNECT)) {
                    getPGP.playerState = CONSTANTS.STATE.PLAYING;
                    await updatePlayerGamePlay(getPGP);
                }

            }

            await reGenrateBull(tableId, userId);

            return Promise.resolve(job.data);

        } else {
            return Promise.reject("Invalid Request Data [gameStartExtendProcess]");
        }

    } catch (error) {
        logger.info("[gameStartExtendProcess] Error: ", error);
        return Promise.reject(error);
    }
}


export const reGenrateBull = async (tableId: string, userId: string): Promise<void> => {

    try {

        const { RANDOM_BALL_TIMER } = getconfig().GAMEPLAY;

        const jobKey: Bull.JobId = `${CONSTANTS.BULL.RANDOM_BALL_BULL}:${tableId}`;

        const job = await randomBallTimer.getJob(jobKey);
        if (job) { job.remove(); }
        const bullData = { tableId, userId };

        await randomBallTimerQueue(bullData);

        let getTGP = await getTableGamePlay(tableId);
        if (!getTGP) { throw new Error("Get Table Game Play Data Failed!"); }

        const color = await getColor(getTGP.randomNumbers);
        const responseData: I_COLOR_BALL_TIMER = {
            timer: RANDOM_BALL_TIMER,
            ballNumber: getTGP.totalNumbers[0],
            color: color
        }

        const responseData2: I_COLOR_BALL_TIMER = {
            timer: 0,
            ballNumber: getTGP.totalNumbers[0],
            color: color
        }

        getTGP.colorBall.unshift(responseData2);
        if (getTGP.colorBall.length > 5) {
            getTGP.colorBall.pop();
        }

        Emitter.emit(CONSTANTS.EVENTS.RANDOM_BALL, {
            sentId: tableId,
            data: responseData,
            message: CONSTANTS.RESPONSE.RANDOM_BALL,
        });

        getTGP.randomNumbers.splice(0, 0, getTGP.totalNumbers[0]);
        getTGP.totalNumbers.shift();

        getTGP = await updateTableGamePlay(getTGP);
        if (!getTGP) { throw new Error("Update Table Game Play Data Failed!"); }

    } catch (error) {
        logger.info("[reGenrateBull] Error : ", error)
    }

}