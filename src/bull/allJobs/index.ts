import Bull from 'bull';
import { getconfig } from '../../config';
import { CONSTANTS } from '../../constants';

const { REDIS } = getconfig();

const BullRedisOptions: Bull.QueueOptions = {

    redis: {
        host: REDIS.REDIS_HOST,
        port: Number(REDIS.REDIS_PORT),
        password: REDIS.REDIS_PASSWORD,
        db: Number(REDIS.REDIS_DATABASE_NUMBER)
    },
    prefix: 'Bull'
}

const gameStartTimer = new Bull(CONSTANTS.BULL.GAME_START_BULL, BullRedisOptions);
const gameStartExtendTimer = new Bull(CONSTANTS.BULL.GAME_START_EXTEND_BULL, BullRedisOptions);
const matchMakingTimer = new Bull(CONSTANTS.BULL.MATCHMAKING_BULL, BullRedisOptions);
const disconnectTimer = new Bull(CONSTANTS.BULL.DISCONNECT_BULL, BullRedisOptions);
const randomBallTimer = new Bull(CONSTANTS.BULL.RANDOM_BALL_BULL, BullRedisOptions);
const delayWinningTimer = new Bull(CONSTANTS.BULL.DELAY_WINNING_BULL, BullRedisOptions);

export {

    matchMakingTimer,
    gameStartTimer,
    gameStartExtendTimer,
    disconnectTimer,
    randomBallTimer,
    delayWinningTimer

}