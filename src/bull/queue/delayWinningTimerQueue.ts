import <PERSON>, { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "bull";
import { CONSTANTS } from "../../constants";
import { getconfig } from "../../config";
import { I_WINNING_TIMER } from "../../interface/common";
import { delayWinningTimer } from "../allJobs";
import { delayWinningProcess } from "../process/delayWinningTimerProcess";


const delayWinningQueue = async (
    data: I_WINNING_TIMER
) => {

    try {

        const { tableId, userId } = data;
        const { WINNING_DELAY_TIMER } = getconfig().GAMEPLAY;

        if (tableId && userId && WINNING_DELAY_TIMER) {

            const jobKey: Bull.JobId = `${CONSTANTS.BULL.DELAY_WINNING_BULL}:${tableId}`;

            if (data.isDisconectOrLeave) {

                const options: Bull.JobOptions = {
                    delay: 0,
                    jobId: jobKey,
                    removeOnComplete: true,
                }

                await delayWinningTimer.add(data, options);

            } else {

                const options: Bull.JobOptions = {
                    delay: Number(WINNING_DELAY_TIMER * 1000),
                    jobId: jobKey,
                    removeOnComplete: true,
                }

                await delayWinningTimer.add(data, options);

            }

        } else {
            throw new Error("delayWinningQueue Request Data is empty");
        }

    } catch (error) {
        console.log("delayWinningQueue Catch Error : ", error);
        return error;
    }

}

delayWinningTimer.process(async (job: Job<I_WINNING_TIMER>, done: DoneCallback) => {
    return await delayWinningProcess(job, done);
})

export { delayWinningQueue };