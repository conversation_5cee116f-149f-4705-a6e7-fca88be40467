import <PERSON>, { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "bull";
import { I_GAME_START } from "../../interface/common";
import { CONSTANTS } from "../../constants";
import { getconfig } from "../../config";
import { gameStartTimer } from "../allJobs";
import { gameStartProcess } from "../process/gameStartProcess";


const gameStartQueue = async (
    data: I_GAME_START
) => {

    try {

        const { tableId, userId } = data;
        const { GAME_START_TIMER } = getconfig().GAMEPLAY;

        if (tableId && userId && GAME_START_TIMER) {

            const jobKey: Bull.JobId = `${CONSTANTS.BULL.GAME_START_BULL}:${tableId}`;

            const options: Bull.JobOptions = {
                delay: Number(GAME_START_TIMER * 1000),
                jobId: jobKey,
                removeOnComplete: true,
            }

            await gameStartTimer.add(data, options);

        } else {
            throw new Error("gameStartQueue Request Data is empty");
        }

    } catch (error) {
        console.log("gameStartQueue Catch Error : ", error);
        return error;
    }

}

gameStartTimer.process(async (job: Job<I_GAME_START>, done: DoneCallback) => {
    return await gameStartProcess(job, done);
})

export { gameStartQueue };