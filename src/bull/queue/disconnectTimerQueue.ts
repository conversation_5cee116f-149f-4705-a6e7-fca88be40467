import Bull, { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "bull";
import { CONSTANTS } from "../../constants";
import { getconfig } from "../../config";
import { I_DISCONNECT_TIMER } from "../../interface/common";
import { disconnectTimer } from "../allJobs";
import { disconnectProcess } from "../process/disconnectTimerProcess";
import logger from "../../logger";


const disconnectTimerQueue = async (
    data: I_DISCONNECT_TIMER
) => {

    try {

        logger.info("Disconnect Timer Queue : ", { userId: data.userId, tableId: data.tableId })

        const { tableId, userId } = data;
        const { DISCONNECT_TIMER } = getconfig().GAMEPLAY;

        if (tableId && userId && DISCONNECT_TIMER) {

            const jobKey: Bull.JobId = `${CONSTANTS.BULL.DISCONNECT_BULL}:${userId}:${tableId}`;

            const options: Bull.JobOptions = {
                delay: Number(DISCONNECT_TIMER * 1000),
                jobId: jobKey,
                removeOnComplete: true,
            }

            await disconnectTimer.add(data, options);

        } else {
            throw new Error("disconnectTimerQueue Request Data is empty");
        }

    } catch (error) {
        logger.info("disconnectTimerQueue Catch Error : ", error);
        return error;
    }

}

disconnectTimer.process(async (job: Job<I_DISCONNECT_TIMER>, done: DoneCallback) => {
    return await disconnectProcess(job, done);
})

export { disconnectTimerQueue };