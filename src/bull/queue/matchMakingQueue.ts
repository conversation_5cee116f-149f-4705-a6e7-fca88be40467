import Bull, { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "bull";
import { matchMakingTimer } from "../allJobs";
import { BULL_MATCH_MAKING } from "../../interface/common";
import { CONSTANTS } from "../../constants";
import { getconfig } from "../../config";
import logger from "../../logger";
import { matchMakingProcess } from "../process/matchMakingProcess";


const matchMakingTimerQueue = async (
    data: BULL_MATCH_MAKING
) => {

    try {

        const { tableId, userId } = data;
        const { MATCH_MAKING_TIMER } = getconfig().GAMEPLAY;
        logger.info("matchMakingTimerQueue [tableId | userId | MATCH_MAKING_TIMER]", { tableId, userId, MATCH_MAKING_TIMER });

        if (tableId && userId && MATCH_MAKING_TIMER) {

            const jobKey: Bull.JobId = `${CONSTANTS.BULL.MATCHMAKING_BULL}:${tableId}`;

            const options: Bull.JobOptions = {
                delay: Number(MATCH_MAKING_TIMER * 1000),
                jobId: jobKey,
                removeOnComplete: true,
            }

            await matchMakingTimer.add(data, options);

        } else {
            throw new Error("matchMakingTimerQueue request data is Not Valid.");
        }

    } catch (error) {
        console.log("matchMakingTimerQueue Catch Error : ", error);
        return error;
    }

}

matchMakingTimer.process(async (job: Job<BULL_MATCH_MAKING>, done: DoneCallback) => {
    return await matchMakingProcess(job, done);
});

export { matchMakingTimerQueue };