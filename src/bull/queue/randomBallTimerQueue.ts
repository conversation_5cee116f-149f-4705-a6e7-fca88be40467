import Bull, { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "bull";
import { getconfig } from "../../config";
import { CONSTANTS } from "../../constants";
import { randomBallTimer } from "../allJobs";
import { I_RANDOM_BALL_TIMER } from "../../interface/common";
import { randomBullTimerProcess } from "../process/randomBallTimerProcess";


const randomBallTimerQueue = async (
    data: I_RANDOM_BALL_TIMER,
) => {

    try {

        const { tableId, userId } = data;
        const { RANDOM_BALL_TIMER } = getconfig().GAMEPLAY;

        if (tableId && userId && RANDOM_BALL_TIMER) {

            const jobKey: Bull.JobId = `${CONSTANTS.BULL.RANDOM_BALL_BULL}:${tableId}`;

            const options: Bull.JobOptions = {
                delay: Number(RANDOM_BALL_TIMER * 1000),
                jobId: jobKey,
                removeOnComplete: true,
            }

            await randomBallTimer.add(data, options);

        } else {
            throw new Error("randomBallTimerQueue Request Data is empty");
        }

    } catch (error) {
        console.log("randomBallTimerQueue Catch Error : ", error);
        return error;
    }

}

randomBallTimer.process(async (job: Job<I_RANDOM_BALL_TIMER>, done: DoneCallback) => {
    return await randomBullTimerProcess(job, done);
})

export { randomBallTimerQueue };