import Bull, { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "bull";
import { I_GAME_START } from "../../interface/common";
import { CONSTANTS } from "../../constants";
import { getconfig } from "../../config";
import { gameStartExtendTimer } from "../allJobs";
import { gameStartExtendProcess } from "../process/gameStartExtendProcess";
import logger from "../../logger";


const gameStartExtendQueue = async (
    data: I_GAME_START
) => {

    try {

        const { tableId, userId } = data;
        const { GAME_START_DELAY_TIMER } = getconfig().GAMEPLAY;
        logger.info("gameStartExtendQueue : ", GAME_START_DELAY_TIMER);

        if (tableId && userId && GAME_START_DELAY_TIMER) {

            const jobKey: Bull.JobId = `${CONSTANTS.BULL.GAME_START_EXTEND_BULL}:${tableId}`;

            const options: Bull.JobOptions = {
                delay: Number(GAME_START_DELAY_TIMER * 1000),
                jobId: jobKey,
                removeOnComplete: true,
            }

            await gameStartExtendTimer.add(data, options);

        } else {
            throw new Error("gameStartExtendQueue Request Data is empty");
        }

    } catch (error) {
        logger.info("gameStartExtendQueue Catch Error : ", error);
        return error;
    }

}

gameStartExtendTimer.process(async (job: Job<I_GAME_START>, done: DoneCallback) => {
    return await gameStartExtendProcess(job, done);
})

export { gameStartExtendQueue };