import express from 'express';
import { GetPlayerOnlineCount } from '../playing/apiController/getPlayerOnlineCount';
import { GetPlayerOnlineCountLobbyWise } from '../playing/apiController/getPlayerOnlineCountLobbyWise';
import { AllLobbyWiseOnlinePlayer } from '../playing/apiController/allLobbyWiseOnlinePlayer';
import multipleLoginHandler from '../playing/apiController/multipleLoginHandler';

const router = express.Router();

router.get('/test', (req, res) => {
    res.status(200).json({ status: 'ok' });
})

router.get('/', (req, res) => {
    res.status(200).json({ status: 'ok' });
});

// ^ Online Player Routes
router.post('/getOnlinePlayerCount', GetPlayerOnlineCount);
router.post('/getPlayerOnlineCountLobbyWise', GetPlayerOnlineCountLobbyWise);
router.post('/allLobbyWiseOnlinePlayer', AllLobbyWiseOnlinePlayer);
router.post("/multiLoginLogoutFromGameServer", multipleLoginHandler);

export { router };