{"name": "tambola-node-dev", "version": "1.0.0", "description": "Creating a Node.js tambola game project from scratch.", "main": "index.js", "scripts": {"clean": "rimraf build/", "build": "npm run clean && tsc", "start": "npm run build && node build/index.js", "Ts": "nodemon src/index.ts", "Js": "node build/index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@socket.io/redis-adapter": "^8.0.1", "axios": "^1.6.5", "bull": "^4.11.4", "crypto-js": "^4.2.0", "dotenv": "^16.3.1", "express": "^4.18.2", "ioredis": "^5.3.2", "joi": "^17.11.0", "nodemon": "^3.0.1", "redis": "^4.6.11", "redlock": "^5.0.0-beta.2", "rimraf": "^5.0.5", "socket.io": "^4.7.2", "typescript": "^5.3.2", "winston": "^3.11.0"}, "devDependencies": {"@types/bull": "^4.10.0", "@types/crypto-js": "^4.2.1", "@types/express": "^4.17.17", "@types/node": "^20.9.3"}}